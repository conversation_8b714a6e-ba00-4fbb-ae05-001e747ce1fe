import templateEndPoints from '../constants/servicesEndpoints/services/template';
import { getMeetingTemplateNormalizer } from '../normalizers/meetingTemplateNormalizer';
import request from '../toolkit/request';
import type { MeetingTemplate } from '../normalizers/meetingTemplateNormalizer';
import type { PaginatedParams } from '@shared/types/params';
import type { PaginateResponse } from '@shared/types/response';

export type BETemplate = {
  title: string;
  subject: string;
  message: string;
  fileIds: string[];
  isGeneral?: boolean;
} & (
  | {
      hasFollowup: false;
    }
  | {
      hasFollowup: true;
      followupTitle: string;
      followupMessage: string;
      followupPeriod: string;
      default: boolean;
    }
);
export type BETemplateResponse = {
  id: string;
  createdDate: string;
  userId: string;
  pageId: string;
} & BETemplate;

export type EmailTemplateResponse = {
  id: string;
  createdDate: string;
  userId: number;
  pageId: number;
  title: string;
  subject: string;
  message: string;
  timeDelay: string;
  fileIds: number[];
  hasFollowup: boolean;
  followupTitle: string;
  followupMessage: string;
  followupPeriod: string;
  followupFileIds: number[];
  default: boolean;
};

export type EmailTemplateSearchParams = {
  text?: string;
  page?: number;
  size?: number;
};

export type CreateEmailTemplateRequest = {
  title: string;
  subject: string;
  message: string;
  timeDelay: string;
  fileIds: number[];
  hasFollowup: boolean;
  followupTitle?: string;
  followupMessage?: string;
  followupPeriod?: string;
  followupFileIds?: number[];
};

export enum TemplateCategoryTypes {
  rejection = 'rejection',
  message = 'message',
  meeting = 'meeting',
  email = 'email',
}
export type TemplateCategoryType = keyof typeof TemplateCategoryTypes;

export const isValidTemplateCategoryType = (
  value?: string
): value is TemplateCategoryType =>
  !!value && ['rejection', 'message', 'meeting', 'email'].includes(value);

export const searchAllTemplates = async ({
  category,
  ...params
}: PaginatedParams<{ category: TemplateCategoryType }>) => {
  const { data } = await request.get<PaginateResponse<BETemplateResponse>>(
    templateEndPoints.searchAll(category),
    { params }
  );

  return data;
};
export const getAllTemplates = async (
  params: PaginatedParams<{ category: TemplateCategoryType }>
) => {
  const { data } = await request.get<PaginateResponse<BETemplateResponse>>(
    templateEndPoints.getAll,
    { params }
  );

  return data;
};

export const getEmailTemplates = async (
  params: EmailTemplateSearchParams = {}
) => {
  const { data } = await request.get<PaginateResponse<EmailTemplateResponse>>(
    templateEndPoints.emailSearch,
    { params }
  );

  return data;
};
export const getAllMeetingTemplates = async (
  params: PaginatedParams<{ text: string }>
) => {
  const { data } = await request.get<PaginateResponse<BETemplateResponse>>(
    templateEndPoints.getAllMeetings,
    { params }
  );

  return data;
};

export const getMeetingTemplate = async (id: string) => {
  const { data } = await request.get<MeetingTemplate>(
    templateEndPoints.getMeeting(id)
  );

  return getMeetingTemplateNormalizer(data);
};

export const putMeetingTemplate = async (id: string) => {
  const { data } = await request.put<MeetingTemplate>(
    templateEndPoints.getMeeting(id)
  );

  return getMeetingTemplateNormalizer(data);
};

export const setDefaultMeetingTemplate = async (id: string) => {
  const { data } = await request.post<MeetingTemplate>(
    templateEndPoints.setDefaultMeetingTemplate(id),
    {}
  );

  return getMeetingTemplateNormalizer(data);
};

export const deleteDefaultMeetingTemplate = async (id: string) => {
  const { data } = await request.delete<MeetingTemplate>(
    templateEndPoints.setDefaultMeetingTemplate(id)
  );

  return getMeetingTemplateNormalizer(data);
};

export const getTemplate = async ({
  category,
  id,
  ...params
}: {
  category: TemplateCategoryType;
  id?: string;
}) => {
  const data = await request.get<BETemplateResponse>(
    templateEndPoints.byId(category, id),
    { params }
  );

  return data.data;
};

export const createEmailTemplate = async (
  templateData: CreateEmailTemplateRequest
) => {
  const { data } = await request.post<EmailTemplateResponse>(
    templateEndPoints.createEmail,
    templateData
  );
  return data;
};

export const getEmailTemplateById = async (id: number) => {
  const { data } = await request.get<EmailTemplateResponse>(
    templateEndPoints.getEmailTemplate(id)
  );

  return data;
};

export const updateEmailTemplate = async (
  id: number,
  templateData: CreateEmailTemplateRequest
) => {
  const { data } = await request.put<EmailTemplateResponse>(
    templateEndPoints.updateEmailTemplate(id),
    templateData
  );

  return data;
};

export const setEmailTemplateDefault = async (id: number) => {
  const { data } = await request.post<EmailTemplateResponse>(
    templateEndPoints.setEmailTemplateDefault(id),
    {}
  );

  return data;
};

export const removeEmailTemplateDefault = async (id: number) => {
  const { data } = await request.delete<EmailTemplateResponse>(
    templateEndPoints.removeEmailTemplateDefault(id),
    undefined
  );

  return data;
};

export const deleteEmailTemplate = async (id: number) => {
  const { data } = await request.delete<EmailTemplateResponse>(
    templateEndPoints.removeEmailTemplate(id),
    undefined
  );

  return data;
};

// Rejection Template APIs
export const searchRejectionTemplates = async ({
  text = '',
  page = 0,
  size = 10,
}) => {
  const { data } = await request.get(templateEndPoints.all('rejection'), {
    params: { text, page, size },
  });
  return data;
};

export const getRejectionTemplateById = async (id: string | number) => {
  const { data } = await request.get(
    templateEndPoints.byId('rejection', String(id))
  );

  return data;
};

export const updateRejectionTemplate = async (
  id: string | number,
  body: { title: string; subject: string; message: string; fileIds: number[] }
) => {
  const { data } = await request.put(
    templateEndPoints.byId('rejection', String(id)),
    body
  );

  return data;
};

export const deleteRejectionTemplate = async (id: string | number) => {
  const { data } = await request.delete(
    templateEndPoints.byId('rejection', String(id))
  );

  return data;
};

export const setDefaultRejectionTemplate = async (id: string | number) => {
  const { data } = await request.post(
    `${templateEndPoints.byId('rejection')}/default/${id}`,
    {}
  );

  return data;
};

export const removeDefaultRejectionTemplate = async (id: string | number) => {
  const { data } = await request.delete(
    `${templateEndPoints.byId('rejection')}/default/${id}`,
    undefined
  );
  return data;
};

// AutoMessage Template API functions
export const getMessageTemplates = async (
  params: EmailTemplateSearchParams = {}
) => {
  const { data } = await request.get<PaginateResponse<EmailTemplateResponse>>(
    '/api/v1/template/message',
    { params }
  );
  return data;
};

export const createMessageTemplate = async (
  templateData: CreateEmailTemplateRequest
) => {
  const { data } = await request.post<EmailTemplateResponse>(
    '/api/v1/template/message',
    templateData
  );
  return data;
};

export const getMessageTemplateById = async (id: number) => {
  const { data } = await request.get<EmailTemplateResponse>(
    `/api/v1/template/message/${id}`
  );
  return data;
};

export const updateMessageTemplate = async (
  id: number,
  templateData: CreateEmailTemplateRequest
) => {
  const { data } = await request.put<EmailTemplateResponse>(
    `/api/v1/template/message/${id}`,
    templateData
  );
  return data;
};

export const updateRejectTemplate = async ({
  id,
  body,
}: {
  id: string;
  body: CreateEmailTemplateRequest;
}) => {
  const { data } = await request.put<EmailTemplateResponse>(
    templateEndPoints.rejectionTemplate(id),
    body
  );

  return data;
};

export const setMessageTemplateDefault = async (id: number) => {
  const { data } = await request.post<EmailTemplateResponse>(
    `/api/v1/template/message/default/${id}`,
    {}
  );
  return data;
};

export const removeMessageTemplateDefault = async (id: number) => {
  const { data } = await request.delete<EmailTemplateResponse>(
    `/api/v1/template/message/default/${id}`,
    undefined
  );
  return data;
};

export const toggleDefaultTemplate = async ({
  id,
  category,
  isDefault = false,
}: {
  id: string;
  isDefault: boolean;
  category: TemplateCategoryType;
}) => {
  const url = templateEndPoints.toggleDefault(category, id);

  if (isDefault) {
    const data = await request.delete(url);

    return data.data;
  }
  const data = await request.post(url, {});

  return data.data;
};

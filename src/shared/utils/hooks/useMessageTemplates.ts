import React, { useState, useCallback } from 'react';
import type { PaginateResponse } from '@shared/types/response';
import type {
  EmailTemplateResponse,
  EmailTemplateSearchParams,
} from '../api/template';
import { getMessageTemplates } from '../api/template';
import useReactQuery from './useReactQuery';
import { QueryKeys } from '../constants';

export default function useMessageTemplates() {
  const [searchParams, setSearchParams] = useState<EmailTemplateSearchParams>({
    page: 0,
    size: 10,
  });

  const messageTemplatesQuery = useReactQuery<
    PaginateResponse<EmailTemplateResponse>
  >({
    action: {
      key: [QueryKeys.getMessageTemplates, searchParams],
      apiFunc: () => getMessageTemplates(searchParams),
    },
  });

  const updateSearchParams = useCallback(
    (newParams: Partial<EmailTemplateSearchParams>) => {
      setSearchParams((prev) => ({
        ...prev,
        ...newParams,
      }));
    },
    []
  );

  return {
    ...messageTemplatesQuery,
    templates: messageTemplatesQuery.data?.content || [],
    totalElements: messageTemplatesQuery.data?.totalElements || 0,
    totalPages: messageTemplatesQuery.data?.totalPages || 0,
    searchParams,
    updateSearchParams,
  };
}

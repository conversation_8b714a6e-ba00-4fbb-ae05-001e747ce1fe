export type MeetingTemplate = {
  id: number;
  createdDate: string;
  userId: number;
  pageId: number;
  title: string;
  subject: string;
  message: string;
  timeDelay: string;
  fileIds: number[];
  hasFollowup: boolean;
  followupTitle: string;
  followupMessage: string;
  followupPeriod: string;
  followupFileIds: number[];
  default: boolean;
  isGeneral: boolean;
};

export type NormalizedMeetingTemplate = {
  title: string;
  subject: string;
  message: string;
  default?: boolean;
  isGeneral: boolean;
};

export const getMeetingTemplateNormalizer = (
  data: MeetingTemplate
): NormalizedMeetingTemplate => {
  const { title, subject, isGeneral, message, default: defaultTemplate } = data;

  return {
    isGeneral,
    title,
    subject,
    message,
    default: defaultTemplate,
  };
};

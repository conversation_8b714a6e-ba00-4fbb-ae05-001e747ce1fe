import React, { Fragment, memo, useEffect } from 'react';
import useIsFormSubmittedStore from '@shared/utils/hooks/useIsFormSubmittedStore';
import Divider from '../Divider';
import Flex from '../Flex';
import cnj from '../utils/cnj';
import componentBuilder from './ComponentBuilder';
import classes from './DynamicFormBuilder.module.scss';
import FormGroupHeader from './FormGroupHeader';

export interface DynamicFormBuilderProps {
  groups: Array<any>;
  className?: string;
}

const DynamicFormBuilder: React.FC<DynamicFormBuilderProps> = ({
  groups,
  className,
}) => {
  const lastIndex = (groups?.length ?? 0) - 1;
  const { setIsSubmitted } = useIsFormSubmittedStore();

  useEffect(() => {
    setIsSubmitted(false);
  }, []);

  return (
    <Flex className={cnj(classes.formRoot, className)}>
      {groups.map((item: any, index) => {
        const isFirstHalfWidth = item?.isFirstHalfWidth;
        const isSecondHalfWidth = item?.isSecondHalfWidth;
        const secondHalfWidthItem = groups?.[index + 1]?.isSecondHalfWidth
          ? groups?.[index + 1]
          : null;

        // Compose wrapper className: SCSS + wrapStyle
        const wrapperClassName = cnj(classes.formItem, item.wrapStyle);
        const secondWrapperClassName = secondHalfWidthItem
          ? cnj(classes.formItem, secondHalfWidthItem.wrapStyle)
          : undefined;

        if (isSecondHalfWidth) return null;

        return (
          <Fragment key={`dynamic_form_comp_${item.name || item.id || index}`}>
            {isFirstHalfWidth ? (
              <Flex
                flexDir="row"
                className={cnj(
                  classes.rowContainerClassName,
                  item?.rowContainerClassName
                )}
              >
                <Flex data-name={item.name} className={wrapperClassName}>
                  {/* @ts-ignore */}
                  {item.formGroup && <FormGroupHeader {...item.formGroup} />}
                  {componentBuilder({ ...item })}
                </Flex>
                <Flex
                  // key={secondHalfWidthItem.name}
                  data-name={secondHalfWidthItem?.name}
                  className={secondWrapperClassName}
                >
                  {/* @ts-ignore */}
                  {item.formGroup && (
                    <FormGroupHeader {...secondHalfWidthItem.formGroup} />
                  )}
                  {componentBuilder({ ...secondHalfWidthItem })}
                </Flex>
              </Flex>
            ) : (
              <Flex data-name={item.name} className={wrapperClassName}>
                {/* @ts-ignore */}
                {item.formGroup && <FormGroupHeader {...item.formGroup} />}
                {componentBuilder({ ...item })}
              </Flex>
            )}

            {!!item?.divider && lastIndex !== index && (
              <Divider className={item?.divider?.className} />
            )}
          </Fragment>
        );
      })}
    </Flex>
  );
};

export default memo(DynamicFormBuilder);

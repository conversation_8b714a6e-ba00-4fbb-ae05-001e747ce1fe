import { useConfirmContext } from './confirm.provider';
import type { IConfirmContext } from './confirm.provider';
import type { ConfirmationProps } from './Confirmation';
import type { UseConfirmProps } from './useConfirm';

type Config = {
  variant?: ConfirmationProps['variant'];
  styles?: UseConfirmProps['styles'];
  isNarrow?: boolean;
};

const useOpenConfirm = ({
  variant,
  styles,
  isNarrow,
}: Config = {}): IConfirmContext => {
  // const { openConfirmDialog: open, closeConfirm } = useConfirmActions();
  const { openConfirmDialog: open, closeConfirm } = useConfirmContext();

  const openConfirmDialog = (props: UseConfirmProps) => {
    open({ ...props, variant, isNarrow, styles });
  };

  return { openConfirmDialog, closeConfirm };
};

export default useOpenConfirm;

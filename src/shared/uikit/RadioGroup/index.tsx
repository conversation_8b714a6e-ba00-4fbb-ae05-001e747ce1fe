import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import IconButton from '../Button/IconButton';
import Flex from '../Flex/Flex.component';
import Ripple from '../Ripple';
import Tooltip from '../Tooltip';
import OverflowTip from '../Typography/OverflowTip';
import cnj from '../utils/cnj';
import classes from './index.module.scss';
import type { TooltipProps } from '../Tooltip/Tooltip';
import type { TextProps } from '../Typography';

// Note: use prop content to pass the tooltip content.
type HintProps = Omit<TooltipProps, 'children'>;

type Option = {
  value: string | number;
  label: string;
  children?: ReactNode;
  hint?: HintProps;
  rightComponent?: ReactNode;
};

interface RadioGroupProps {
  value: string | number;
  className?: string;
  label?: string;
  onChange: Function;
  options: Array<Option>;
  labelProps?: TextProps;
  disabledReadOnly?: boolean;
  classNames?: {
    root?: string;
    itemWrapper?: string;
    container?: string;
  };
}

const RadioGroup = ({
  value,
  className,
  onChange,
  options,
  labelProps = {},
  disabledReadOnly,
  classNames,
}: RadioGroupProps) => {
  const { t } = useTranslation();
  const onClickButton = (item: Option) => {
    if (disabledReadOnly) return;
    onChange(item.value);
  };

  return (
    <Flex className={classNames?.root}>
      {options?.map((item) => {
        const isSelected = item.value === value;

        return (
          <Flex
            flexDir="row"
            className={cnj('items-center gap-8', classNames?.container)}
            key={item.value}
          >
            <Flex className={cnj(classes.itemWrapper, classNames?.itemWrapper)}>
              <Ripple
                onClick={() => onClickButton(item)}
                disabled={disabledReadOnly}
              >
                <Flex className={cnj(classes.radioGroupRoot, className)}>
                  <IconButton
                    className={cnj(
                      classes.icon,
                      isSelected && classes.selectedIcon
                    )}
                    name={isSelected ? 'dot-circle' : 'circle'}
                    type="far"
                    colorSchema={
                      disabledReadOnly
                        ? 'gray'
                        : isSelected
                          ? 'tertiary-transparent'
                          : 'transparent2'
                    }
                    size="sm22"
                  />
                  <Flex
                    className={cnj(
                      classes.overflowHidden,
                      classes.radioGroupLabelWrapper
                    )}
                  >
                    <OverflowTip
                      size={15}
                      height={21}
                      color={
                        disabledReadOnly ? 'gray' : 'disabledGray_graphene'
                      }
                      lineNumber={1}
                      isTruncated
                      {...labelProps}
                    >
                      {t(item.label)}
                    </OverflowTip>
                  </Flex>
                  {item?.hint ? (
                    <Tooltip
                      key={`${item.value}-description`}
                      trigger={
                        <IconButton
                          name="info-circle"
                          type="far"
                          colorSchema="gray"
                          size="sm22"
                        />
                      }
                      placement="top-end"
                      disabledOnMobile={false}
                      tooltipWrapperProps={{
                        color: 'coal_disabledGray',
                        // font: '500',
                        size: 13,
                        height: 16,
                        textAlign: 'center',
                        ...item.hint.tooltipWrapperProps,
                        className: cnj(
                          classes.tooltipContent,
                          item?.hint?.tooltipWrapperProps?.className
                        ),
                      }}
                      {...item.hint}
                    >
                      {item.hint.content}
                    </Tooltip>
                  ) : null}
                </Flex>
              </Ripple>
              {isSelected && item.children}
            </Flex>
            {item?.rightComponent}
          </Flex>
        );
      })}
    </Flex>
  );
};

export default RadioGroup;

import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';
import Flex from 'shared/uikit/Flex';
import Icon from 'shared/uikit/Icon';
import MenuItemActions from 'shared/uikit/MenuItem/MenuItemActions';
import MenuItemIcon from 'shared/uikit/MenuItem/MenuItemIcon';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import classes from './index.module.scss';
import type { colorsKeys } from 'shared/uikit/helpers/theme';
import type { ActionProps } from 'shared/uikit/MenuItem/MenuItemActions';
import type {
  IconVariant,
  MenuItemIconProps,
} from 'shared/uikit/MenuItem/MenuItemIcon';

export type MenuItemType<IV extends IconVariant> = {
  active?: boolean;
  action?: ActionProps | (() => React.ReactElement);
  actionProps?: ActionProps;
  actionElement?: React.ReactElement;
  actionAlignment?: 'top' | 'middle';
  actionPlacement?: 'end' | 'middle';
  className?: string;
  title: string;
  link?: string;
  onClick?: (item?: any) => void;
  onClickAction?: (item?: any) => void;
  secondaryLabel?: string;
  subTitle?: string | React.ReactNode;
  titleVariant?: 'sm' | 'lg';
  statusLabel?: string;
  statusLabelColor?: colorsKeys;
  statusLabelClassName?: string;
  withHover?: boolean;
  disabled?: boolean;
  titleClassName?: string;
  actionClassName?: string;
  rightElement?: ReactNode;
} & Partial<MenuItemIconProps<IV>>;

const MenuItem = <IV extends IconVariant>({
  active = false,
  action,
  actionProps,
  actionElement,
  actionAlignment = 'middle',
  actionPlacement = 'end',
  className,
  iconName,
  iconType = 'far',
  iconVariant,
  iconColor,
  iconSize,
  iconBoxSize,
  title,
  link,
  onClick,
  onClickAction,
  secondaryLabel,
  subTitle,
  titleVariant = 'lg',
  statusLabel,
  statusLabelColor = 'success',
  statusLabelClassName,
  withHover,
  disabled,
  titleClassName,
  rightElement,
  actionClassName,
}: MenuItemType<IV>) => (
  <BaseButton
    to={link}
    onClick={onClick}
    className={cnj(
      classes.listItemRoot,
      withHover && classes.withHover,
      active && classes.active,
      className
    )}
    disabled={disabled}
  >
    {iconName && (
      <MenuItemIcon
        iconName={iconName}
        iconType={iconType}
        iconSize={iconSize}
        iconBoxSize={iconBoxSize}
        iconVariant={iconVariant || 'default'}
        iconColor={iconColor}
        active={active}
      />
    )}
    <Flex
      className={cnj(
        { [classes.straightRow]: actionPlacement === 'end' },
        { [classes.revertRow]: actionPlacement === 'middle' }
      )}
    >
      <Flex className={cnj(classes.titles)}>
        <Flex className={cnj(subTitle && classes.multiLine)}>
          <Flex className={classes.line}>
            <Typography
              className={cnj(
                !subTitle
                  ? classes.singleTitle
                  : titleVariant === 'lg'
                    ? classes.bigTitle
                    : classes.smallTitle,
                titleClassName
              )}
              isTruncated
              isWordWrap
              lineNumber={1}
            >
              {title}
            </Typography>
            {secondaryLabel && (
              <Typography className={classes.secondaryLabel}>
                {secondaryLabel}
              </Typography>
            )}
            {statusLabel && (
              <Typography
                className={cnj(classes.statusLabel, statusLabelClassName)}
                color={statusLabelColor}
              >
                {statusLabel}
              </Typography>
            )}
          </Flex>

          {Boolean(subTitle) && (
            <Flex className={classes.line}>
              <Typography
                className={
                  titleVariant === 'lg' ? classes.smallTitle : classes.bigTitle
                }
              >
                {subTitle}
              </Typography>
            </Flex>
          )}
        </Flex>
        {rightElement}
        {active && <Icon name="check-circle" color="white" size={12} />}
      </Flex>

      <Flex
        className={cnj(
          classes.action,
          disabled && classes.disabledAction,
          { [classes.center]: actionAlignment === 'middle' },
          { [classes.top]: actionAlignment === 'top' },
          actionClassName
        )}
      >
        {actionElement ??
          (actionProps ? (
            <MenuItemActions
              action={actionProps}
              className={cnj(
                { [classes.top]: actionAlignment === 'top' },
                { [classes.center]: actionAlignment === 'middle' }
              )}
              onClick={onClickAction}
            />
          ) : typeof action === 'function' ? (
            action()
          ) : action ? (
            <MenuItemActions
              action={action}
              className={cnj(
                { [classes.top]: actionAlignment === 'top' },
                { [classes.center]: actionAlignment === 'middle' }
              )}
              onClick={onClickAction}
            />
          ) : null)}
      </Flex>
    </Flex>
  </BaseButton>
);

export default MenuItem;

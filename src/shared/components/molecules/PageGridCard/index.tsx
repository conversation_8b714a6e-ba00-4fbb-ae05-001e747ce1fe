import React from 'react';
import Avatar from 'shared/uikit/Avatar';
import BaseButton from 'shared/uikit/Button/BaseButton';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import RadioButton from 'shared/uikit/RadioButton';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import PortalAndRoleBadge from '../PortalAndRoleBadge';
import classes from './index.module.scss';
import type { RoleType } from '@shared/types/page';
import type { FC } from 'react';

interface Props {
  avatar: string;
  title: string;
  subTitle: string;
  isPage: boolean;
  isActive?: boolean;
  role: RoleType | RoleType[];
  onClick?: VoidFunction;
  variant?: 'portalItem' | 'normal';
  visibleRightAction?: boolean;
  className?: string;
}

const PageGridCard: FC<Props> = ({
  avatar,
  title,
  subTitle,
  isPage,
  isActive,
  role,
  onClick,
  variant = 'normal',
  visibleRightAction = true,
  className,
}) => (
  <BaseButton onClick={onClick} className={cnj(classes.root, className)}>
    <Flex
      className={cnj(
        classes.content,
        variant === 'portalItem' && classes.content_portalItem
      )}
    >
      {variant !== 'portalItem' && (
        <Avatar
          isCompany={isPage}
          size="xlg"
          imgSrc={avatar}
          bordered
          className={classes.avatarContainer}
        />
      )}

      <Flex>
        {Array.isArray(role) ? (
          <Flex className={classes.roles}>
            {role.map((item) => (
              <PortalAndRoleBadge key={item} role={item} />
            ))}
          </Flex>
        ) : (
          <PortalAndRoleBadge role={role} />
        )}
        <Typography
          mt={variant === 'portalItem' ? 8 : 4}
          color="smoke_coal"
          font="700"
          size={16}
          height={19}
        >
          {title}
        </Typography>
        {variant !== 'portalItem' && (
          <Typography
            mt={2}
            color="secondaryDisabledText"
            size={14}
            height={16}
          >
            {subTitle}
          </Typography>
        )}
      </Flex>
      {visibleRightAction &&
        (variant !== 'portalItem' ? (
          <RadioButton
            styles={{ root: classes.radioButton }}
            value={isActive}
          />
        ) : (
          <IconButton
            colorSchema="transparent"
            name="chevron-right"
            size="sm16"
            type="far"
            noHover
            className={classes.radioButton}
          />
        ))}
    </Flex>
  </BaseButton>
);

export default PageGridCard;

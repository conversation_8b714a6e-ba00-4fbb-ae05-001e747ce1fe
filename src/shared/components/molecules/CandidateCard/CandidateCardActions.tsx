import { useCallback, useMemo, type FC } from 'react';
import { useLinkJobsToCandidate } from '@shared/components/Organism/AsyncPickerModal/presets/job/hooks';
import { CandidateFormStepKeys } from '@shared/components/Organism/MultiStepForm/CreateCandidateForm/constants';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import {
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { sendCandidateInvitation } from '@shared/utils/api/candidates';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import { candidateFormDateNormalizer } from '@shared/utils/normalizers/compareNormalizer';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Menu from '../Menu/Menu';
import type { CandidateFormData } from '@shared/types/candidates';
import type { MenuItem } from '@shared/types/components/Menu.type';

export interface CandidateCardActionsProps {
  candidate: CandidateFormData;
}

const CandidateCardActions: FC<CandidateCardActionsProps> = (props) => {
  const { candidate } = props;
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();
  const { data: submitToClientFormState } =
    useMultiStepFormState('submitToClientForm');

  const linkJobsAsyncPicker = useLinkJobsToCandidate(candidate);

  const appDispatch = useGlobalDispatch();

  const { mutate: inviteCandidateToLobox, isPending: isPendingInvite } =
    useReactMutation({
      apiFunc: sendCandidateInvitation,
    });

  const showInviteToLobox = useMemo(
    () =>
      !!candidate.profile.email?.value &&
      (candidate.isSocialCandidate || candidate.isManuallyCreated),
    [candidate]
  );

  const shareCandidate = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          showCopyId: true,
          attachment: {
            type: ShareEntities.CANDIDATE,
            data: {
              ...candidate,
              isBusiness: true,
            },
          },
        },
      },
    });
  };

  const submitToClient = useCallback(() => {
    openMultiStepForm({
      formName: 'submitToClientForm',
      data: {
        ...submitToClientFormState,
        candidateId: candidate?.id,
      },
      stepKey: 'client',
    });
  }, []);

  const addCandidateToCompare = useCallback(() => {
    appDispatch({
      type: 'TOGGLE_COMPARE_MODAL',
      payload: {
        open: true,
        selectedUsers: [candidateFormDateNormalizer(candidate)],
      },
    });
  }, []);

  const isEditable = !candidate?.locked && !candidate?.isSocialCandidate;

  const candidateOnlyMenu: MenuItem[] = [
    candidate?.countOfJobs === 0
      ? {
          iconName: 'link',
          label: t('link_jobs'),
          onClick: linkJobsAsyncPicker.open,
        }
      : {
          iconName: 'compare',
          label: t('add_to_compare'),
          onClick: addCandidateToCompare,
        },
    {
      iconName: 'share',
      label: t('share'),
      onClick: shareCandidate,
    },
    {
      iconName: 'submit-file',
      label: t('submit_to_client'),
      onClick: submitToClient,
    },
    isEditable
      ? {
          iconName: 'pen-light',
          label: t('edit'),
          onClick: () => {
            openMultiStepForm({
              formName: 'createCandidateForm',
              data: candidate,
              stepKey: candidate?.isLoboxUser
                ? CandidateFormStepKeys.GENERAL
                : CandidateFormStepKeys.NEW,
            });
          },
        }
      : undefined,
    showInviteToLobox
      ? {
          iconName: 'envelope-open-text-light',
          label: t('invite_to_lobox'),
          disabled: isPendingInvite,
          onClick: () => {
            if (isPendingInvite) return;
            inviteCandidateToLobox(
              {
                body: {
                  candidateId: candidate?.id,
                  email: candidate?.profile?.email?.value,
                  sender: true,
                },
              },
              {
                onSuccess: () =>
                  handleSuccess({
                    title: t('invitation_sent'),
                    message: t('candidate_invitation_sent_successfully'),
                  }),
              }
            );
          },
        }
      : undefined,
    !candidate?.isLoboxUser &&
    !candidate?.isLoboxCandidate &&
    !candidate?.isSocialCandidate
      ? {
          iconName: 'trash1',
          label: candidate?.isLoboxUser ? t('delete_data') : t('delete'),
          onClick: () => {
            openMultiStepForm({
              formName: 'deleteEntityModal',
              data: candidate,
              variant: candidate.profile.username
                ? 'deleteCandidate'
                : 'deleteManualCandidate',
            });
          },
        }
      : undefined,
  ].filter(Boolean) as MenuItem[];

  return (
    <Menu
      menuItems={candidateOnlyMenu}
      menuPlacement="bottom-end"
      menuItemSize={20}
      classNames={{ itemIconWrapper: '!m-0', menu: '!p-0' }}
    />
  );
};

export default CandidateCardActions;

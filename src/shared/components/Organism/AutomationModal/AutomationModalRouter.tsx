import React from 'react';
import { AutoMoveRequirementsModal } from '@shared/components/Organism/AutomationModal/components/AutoMove/AutoMoveRequirementsModal';
import { AutoRejectRequirementsModal } from '@shared/components/Organism/AutomationModal/components/Autoreject/AutoRejectRequirementsModal';
import RejectionModal from '@shared/components/Organism/AutomationModal/components/Autoreject/RejectionModal';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import AutomationModal from './AutomationModal';
import AutoInterviewModal from './components/AutoInterview/AutoInterviewModal';
import AutoMessageModal from './components/AutoMessage/AutoMessageModal';
import AutoMoveModal from './components/AutoMove/AutoMoveModal';
import AutoNotesModal from './components/AutoNotes/AutoNotesModal';
import AutoRejectModal from './components/Autoreject/AutoRejectModal';
import AutoRejectStageRejectionModal from './components/Autoreject/AutoRejectStageRejectionModal';
import AutoReplyModal from './components/AutoReply/AutoReplyModal';
import AutoTodoModal from './components/AutoTodo/AutoTodoModal';

const AutomationModalRouter: React.FC = () => {
  const automationState = useMultiStepFormState('automation');

  if (!automationState?.isOpen) {
    return null;
  }

  const modalType = automationState.type || 'main';

  switch (modalType) {
    case 'main':
      return <AutomationModal />;
    case 'autoMove':
      return <AutoMoveModal />;
    case 'autoReject':
      return <AutoRejectModal />;
    case 'reject_stage_rejection':
      return <AutoRejectStageRejectionModal />;
    case 'reject_requirements':
      return <AutoRejectRequirementsModal />;
    case 'move_requirements':
      return <AutoMoveRequirementsModal />;
    case 'autoReply':
      return <AutoReplyModal />;
    case 'autoMessage':
      return <AutoMessageModal />;
    case 'autoInterview':
      return <AutoInterviewModal />;
    case 'autoNotes':
      return <AutoNotesModal />;
    case 'autoTodo':
      return <AutoTodoModal />;
    case 'rejection':
      return <RejectionModal />;
    default:
      return <AutomationModal />;
  }
};

export default AutomationModalRouter;

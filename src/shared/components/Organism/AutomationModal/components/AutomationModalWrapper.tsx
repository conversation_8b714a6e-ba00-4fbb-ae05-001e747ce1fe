import React from 'react';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';

interface AutomationModalWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  backButtonProps?: {
    onClick: () => void;
  };
  noCloseButton?: boolean;
  hideBack?: boolean;
  wide?: boolean;
  modalClassName?: string;
  contentClassName?: string;
  modalDialogClassName?: string;
}

export const AutomationModalWrapper: React.FC<AutomationModalWrapperProps> = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  backButtonProps,
  noCloseButton = false,
  hideBack = false,
  wide = true,
  modalClassName,
  contentClassName,
  modalDialogClassName,
}) => (
  <FixedRightSideModal
    isOpen={isOpen}
    onClose={onClose}
    onClickOutside={onClose}
    isOpenAnimation
    wide={wide}
    modalClassName={modalClassName}
    contentClassName={contentClassName}
    modalDialogClassName={modalDialogClassName}
  >
    <ModalHeaderSimple
      title={title}
      backButtonProps={backButtonProps}
      noCloseButton={noCloseButton}
      hideBack={hideBack}
    />
    <ModalBody className="h-full !p-0 overflow-auto">{children}</ModalBody>
    {footer && <ModalFooter>{footer}</ModalFooter>}
  </FixedRightSideModal>
);

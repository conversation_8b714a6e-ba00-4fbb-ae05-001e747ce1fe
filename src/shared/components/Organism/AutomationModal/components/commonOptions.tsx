import useTranslation from '@shared/utils/hooks/useTranslation';
import type { ActionOption, TypeOption } from './types';

export const useActionOptions = (): ActionOption[] => {
  const { t } = useTranslation();

  return [
    { value: 'location', label: t('location') },
    { value: 'age', label: t('age') },
    { value: 'coverLetter', label: t('cover_letter') },
    { value: 'phoneNumber', label: t('phone_number') },
  ];
};

export const useTypeOptions = (): TypeOption[] => {
  const { t } = useTranslation();

  return [
    { value: 'is', label: t('is') },
    { value: 'isNot', label: t('is_not') },
  ];
};

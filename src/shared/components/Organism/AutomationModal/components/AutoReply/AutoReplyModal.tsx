import React, { useMemo, useState } from 'react';
import EnhancedTemplateForm from '@shared/components/Organism/AutomationModal/components/TemplateForm/EnhancedTemplateForm';
import TemplateList from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateList';
import { useTemplateActions } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateActions';
import { useTemplateForm } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateForm';
import { useTemplateList } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateList';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import {
  deletePipelineAutoReply,
  getPipelineAutoReply,
  putPipelineAutoReply,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';

type AutoReplyModalProps = {
  onModifiedSuccess?: (result: any) => void;
};

const AutoReplyModal: React.FC<AutoReplyModalProps> = ({
  onModifiedSuccess,
}) => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const [showForm, setShowForm] = useState(false);
  const { handleSuccess } = useResponseToast();
  const pipelineId = Number((automationState?.data as any)?.id);

  const {
    data: reply,
    isLoading: isLoadingReplies,
    refetch: refetchAutoReply,
  } = useReactQuery({
    action: {
      apiFunc: () => getPipelineAutoReply(pipelineId),
      key: [QueryKeys.getPipelineAutoReply, pipelineId],
    },
  });

  const { mutate: putPipelineAutoReplyMutation, isPending: isUpdatingPending } =
    useReactMutation({
      apiFunc: putPipelineAutoReply,
      onSuccess: (result) => {
        refetchAutoReply();
        onModifiedSuccess?.(result);
      },
    });

  const {
    mutate: deletePipelineAutoReplyMutation,
    isPending: isDeletePending,
  } = useReactMutation({
    apiFunc: deletePipelineAutoReply,
    onSuccess: (result) => {
      refetchAutoReply();
      onModifiedSuccess?.(result);
    },
  });

  const templateForm = useTemplateForm({
    onFormChange: (_values, _isValid) => {},
    onSubmit: (data, isCreate) => {
      if (isCreate) {
        templateActions.createTemplate(data);
      } else if (templateForm.editingTemplate) {
        templateActions.updateTemplate(
          parseInt(templateForm?.editingTemplate?.id, 10),
          data,
          () => {
            handleSuccess({
              message: t('auto_reply_created_message'),
              title: t('auto_reply_created_title'),
            });
          }
        );
      }
    },
  });

  const templateList = useTemplateList({
    searchEnabled: true,
    onSuccess: (template) => {
      if (template?.length) {
        templateForm.startEditing(
          template?.find(
            (item) => item?.id === templateForm?.editingTemplate?.id
          ) as unknown as NormalizedTemplate
        );
      }
    },
  });

  const isDefaultTemplateLoading =
    isDeletePending || isUpdatingPending || isLoadingReplies;

  const templateActions = useTemplateActions(
    {
      onTemplateCreated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateUpdated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: (templateId, isCurrentlyDefault) => {
        templateList.refetch();

        if (isCurrentlyDefault) {
          deletePipelineAutoReplyMutation(pipelineId);
        } else {
          putPipelineAutoReplyMutation({
            pipelineId,
            body: {
              templateId: Number(templateId),
            },
          });
        }
      },
    },
    {
      message: t('auto_reply_duplicated_message'),
      title: t('auto_reply_duplicated_title'),
    }
  );

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        templateForm.startEditing(template);
        setShowForm(true);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const handleCreate = () => {
    templateForm.startCreating();
    setShowForm(true);
  };

  const handleTemplateClick = (templateId: string) => {
    const template = templateList.getTemplateById(templateId);
    if (template) {
      templateForm.startEditing(template);
      setShowForm(true);
    }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    templateForm.resetForm();
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  const isDefault = useMemo(
    () =>
      !!templateForm?.editingTemplate?.default &&
      !!reply?.templateId &&
      !!templateForm?.editingTemplate?.id &&
      templateForm?.editingTemplate?.id === reply?.templateId,
    [
      templateForm?.editingTemplate?.default,
      templateForm?.editingTemplate?.id,
      reply?.templateId,
    ]
  );

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={
          showForm
            ? templateForm.isEditing
              ? t('edit_template')
              : t('create_template')
            : t('auto_reply')
        }
        backButtonProps={{
          onClick: showForm ? handleFormDiscard : handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody className="p-6 h-full overflow-auto mb-8">
        {showForm ? (
          <EnhancedTemplateForm
            isDefaultTemplate={isDefault}
            onSetDefault={() =>
              templateForm?.editingTemplate?.id &&
              templateActions.setDefaultTemplate(
                templateForm?.editingTemplate?.id,
                templateForm?.editingTemplate?.default
              )
            }
            isUpdatingDefault={
              (templateForm?.editingTemplate?.id &&
                templateActions.isUpdatingDefault ===
                  templateForm.editingTemplate.id) ||
              templateActions?.isMutating
            }
            formData={templateForm.formData}
            onSubmit={templateForm.handleSubmit}
            isLoading={templateForm.isLoading}
            onChange={templateForm.handleFormChange}
            config={{
              showDelay: true,
              showFollowup: true,
              showAttachments: true,
            }}
          />
        ) : (
          <TemplateList
            templates={templateList.templates}
            isLoading={templateList.isLoading}
            searchQuery={templateList.searchQuery}
            defaultTemplateId={reply?.templateId}
            onSearchChange={templateList.handleSearchChange}
            onTemplateClick={handleTemplateClick}
            onSetDefault={handleSetDefault}
            actions={templateActionsList}
            isUpdatingDefault={templateActions.isUpdatingDefault}
            isDefaultTemplateLoading={isDefaultTemplateLoading}
            config={{
              showSearch: true,
              showActions: true,
              showDefaultToggle: true,
            }}
          />
        )}
      </ModalBody>

      <ModalFooter>
        {showForm ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={templateForm.isEditing ? t('update') : t('create')}
              schema="primary-blue"
              variant="default"
              onClick={() => templateForm.handleSubmit()}
              className="flex-1"
              disabled={!templateForm.canSubmit}
              isLoading={templateForm.isLoading || templateActions.isMutating}
            />
          </Flex>
        ) : (
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={handleCreate}
          />
        )}
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default AutoReplyModal;

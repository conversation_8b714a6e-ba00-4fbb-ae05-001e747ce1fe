import React from 'react';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { TemplateSearchProps } from '@shared/components/Organism/AutomationModal/types/template.types';

const TemplateSearch: React.FC<TemplateSearchProps> = ({
  placeholder,
  value,
  onChange,
  className,
}) => {
  const { t } = useTranslation();

  return (
    <SearchInputV2
      placeholder={placeholder || t('search_templates')}
      onChange={onChange}
      value={value}
      className={className}
    />
  );
};

export default TemplateSearch;

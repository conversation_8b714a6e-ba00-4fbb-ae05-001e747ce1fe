import { useState, useMemo } from 'react';
import useEmailTemplates from '@shared/utils/hooks/useEmailTemplates';
import type {
  NormalizedTemplate,
  UseTemplateListOptions,
} from '@shared/components/Organism/AutomationModal/types/template.types';

const defaultTransformTemplate = (template: any): NormalizedTemplate => ({
  id: template.id.toString(),
  title: template.title || '',
  subject: template.subject || '',
  message: template.message || '',
  fileIds: (template.fileIds || []).map((id: any) => id.toString()),
  hasFollowup: template.hasFollowup || false,
  default: template.default || false,
  followupPeriod: template.followupPeriod,
  followupTitle: template.followupTitle,
  followupMessage: template.followupMessage,
  followupAttachments: template.followupAttachments,
});

export const useTemplateList = (options: UseTemplateListOptions = {}) => {
  const {
    searchEnabled = true,
    defaultSearchQuery = '',
    transformTemplate = defaultTransformTemplate,
    onSuccess,
  } = options;

  const [searchQuery, setSearchQuery] = useState(defaultSearchQuery);
  const {
    templates: apiTemplates,
    isLoading,
    refetch,
  } = useEmailTemplates({ onSuccess });

  const templates = useMemo(() => {
    if (!apiTemplates) return [];

    return apiTemplates.map(transformTemplate);
  }, [apiTemplates, transformTemplate]);

  const filteredTemplates = useMemo(() => {
    if (!searchEnabled || !searchQuery.trim()) {
      return templates;
    }

    const query = searchQuery.toLowerCase();

    return templates.filter(
      (template) =>
        template.title.toLowerCase().includes(query) ||
        template.subject.toLowerCase().includes(query)
    );
  }, [templates, searchQuery, searchEnabled]);

  const defaultTemplate = useMemo(
    () => templates.find((template) => template.default),
    [templates]
  );

  const handleSearchChange = (query: string) => {
    if (searchEnabled) {
      setSearchQuery(query);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  return {
    templates: filteredTemplates,
    allTemplates: templates,
    defaultTemplate,
    isLoading,
    searchQuery,
    hasResults: filteredTemplates.length > 0,
    totalCount: templates.length,
    filteredCount: filteredTemplates.length,
    refetch,
    handleSearchChange,
    clearSearch,
    setSearchQuery,
    getTemplateById: (id: string) => templates.find((t) => t.id === id),
    isFiltered: searchQuery.trim().length > 0,
  };
};

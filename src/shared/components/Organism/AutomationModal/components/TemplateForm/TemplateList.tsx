import React from 'react';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import MenuItem from '@shared/uikit/MenuItem';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import useDisclosure from '@shared/utils/hooks/useDisclosure';
import useTranslation from '@shared/utils/hooks/useTranslation';
import TemplateActions from './TemplateActions';
import TemplateSearch from './TemplateSearch';
import type { TemplateListProps } from '../../types/template.types';

const TemplateList: React.FC<TemplateListProps> = ({
  templates,
  isLoading = false,
  searchQuery = '',
  onSearchChange,
  onTemplateClick,
  onSetDefault,
  actions = [],
  config = {},
  isUpdatingDefault,
  defaultTemplateId,
  checkingDefaultWithApi = true,
  isDefaultTemplateLoading,
}) => {
  const { t } = useTranslation();
  const { isOpen, onClose, onOpen } = useDisclosure();

  const {
    searchPlaceholder,
    showSearch = true,
    showActions = true,
    showDefaultToggle = true,
    emptyStateMessage,
  } = config;

  const renderLoadingSkeleton = () => (
    <Flex flexDir="column" className="gap-12 px-0">
      {[...Array(10).keys()].map((_, index) => (
        <Skeleton key={index} className="h-[56px] w-full rounded-lg" />
      ))}
    </Flex>
  );

  const renderEmptyState = () => (
    <Typography className="text-center text-sm font-bold">
      {emptyStateMessage || t('no_templates_found')}
    </Typography>
  );

  const renderTemplateItem = (template: any) => {
    const matchesDefaultId =
      template?.id && defaultTemplateId && template?.id === defaultTemplateId;
    const hasDefaultFlag = template.default;

    const isDefault = checkingDefaultWithApi
      ? Boolean(matchesDefaultId && hasDefaultFlag)
      : hasDefaultFlag;

    return (
      <MenuItem
        key={template.id}
        title={template.title}
        titleClassName="text-lg font-semibold"
        actionClassName="justify-end"
        subTitle={
          <Typography className="flex flex-col mt-4 text-xs whitespace-nowrap">
            <Typography className="!text-colorIconForth2 text-sm font-medium">
              {t('subject')}:
            </Typography>
            <Typography
              isTruncated
              className="text-primaryText text-xs font-normal"
            >
              {template.subject}
            </Typography>
          </Typography>
        }
        className="!rounded !p-12 overflow-hidden flex justify-between items-center border border-solid !border-techGray_20 !bg-gray_5 hover:border-brand hover:bg-brand-50 shrink-0"
        onClick={() => onTemplateClick?.(template.id)}
        actionElement={
          <Flex className="justify-between items-end gap-12 h-full">
            {showActions && actions.length > 0 ? (
              <TemplateActions
                template={template}
                actions={actions}
                isOpen={isOpen}
                onOpen={onOpen}
                onClose={onClose}
              />
            ) : (
              <Flex />
            )}
            {showDefaultToggle && onSetDefault && (
              <Button
                schema={
                  isDefault ? 'success-semi-transparent' : 'secondary-dark'
                }
                variant="thin"
                label={isDefault ? t('default') : t('set_as_default')}
                leftIcon={isDefault ? 'check' : undefined}
                leftType="fas"
                labelColor={isDefault ? 'success' : 'primaryText'}
                onClick={(e) => {
                  e?.stopPropagation();
                  onSetDefault(template.id, isDefault, e);
                }}
                disabled={isUpdatingDefault === template.id}
                isLoading={isUpdatingDefault === template.id}
              />
            )}
          </Flex>
        }
      />
    );
  };

  return (
    <Flex className="flex-col h-full gap-8">
      <Flex className="flex-col h-full gap-20">
        {showSearch && onSearchChange && (
          <TemplateSearch
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={onSearchChange}
          />
        )}

        <Flex className="flex-col gap-12 flex-1 overflow-y-auto">
          {isLoading
            ? renderLoadingSkeleton()
            : templates.length === 0
              ? renderEmptyState()
              : templates.map(renderTemplateItem)}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default TemplateList;

export const formatTime = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);

    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  } catch {
    return '';
  }
};

export const mapFileIdsToAttachments = (
  fileIds: string[] | number[]
): Array<{ id: string }> => (fileIds || []).map((id) => ({ id: String(id) }));

export const mapAttachmentsToFileIds = (
  attachments: Array<{ id: string }>
): string[] => (attachments || []).map((a) => a.id);

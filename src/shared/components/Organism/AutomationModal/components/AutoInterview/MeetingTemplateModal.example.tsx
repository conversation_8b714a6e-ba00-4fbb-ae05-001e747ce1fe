import React, { useState } from 'react';
import Button from 'shared/uikit/Button';
import { MeetingTemplateModal } from './index';
import type { MeetingTemplateFormData } from './types';

/**
 * Example usage of the MeetingTemplateModal component
 * This demonstrates how to integrate the reusable meeting template modal
 */
const MeetingTemplateModalExample: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Example initial data for editing mode
  const initialData: Partial<MeetingTemplateFormData> = {
    title: 'Weekly Team Standup',
    description: 'Weekly team sync to discuss progress and blockers',
    duration: { label: '30 minutes', value: '_30_MINUTES' },
    remind: { label: '15 minutes before', value: '_15_MIN_BEFORE' },
  };

  // Handle form submission
  const handleSubmit = async (data: MeetingTemplateFormData) => {
    setIsLoading(true);

    try {
      // Simulate API call
      console.log('Submitting meeting template:', data);
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Close modal on success
      setIsModalOpen(false);
      alert('Meeting template created successfully!');
    } catch (error) {
      console.error('Failed to create meeting template:', error);
      alert('Failed to create meeting template');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle success callback
  const handleSuccess = (response: any) => {
    console.log('Success response:', response);
    setIsModalOpen(false);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Meeting Template Modal Example</h2>

      <div style={{ marginBottom: '20px' }}>
        <Button
          label="Create New Meeting Template"
          schema="primary-blue"
          onClick={() => setIsModalOpen(true)}
        />
      </div>

      <MeetingTemplateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleSubmit}
        onSuccess={handleSuccess}
        title="Create Meeting Template"
        submitButtonText="Create Template"
        isLoading={isLoading}
        mode="create"
        wide={true}
        // Optional: provide initial data for edit mode
        // initialData={initialData}
        // Optional: configure form fields
        // config={{
        //   showDatetime: true,
        //   showDuration: true,
        //   showLocation: true,
        //   showAttendees: true,
        //   showAttachments: true,
        //   showPermissions: true,
        // }}
      />

      <div style={{ marginTop: '40px' }}>
        <h3>Usage Examples:</h3>
        <pre
          style={{
            backgroundColor: '#f5f5f5',
            padding: '15px',
            borderRadius: '5px',
            fontSize: '12px',
            overflow: 'auto',
          }}
        >
          {`// Basic usage
<MeetingTemplateModal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  onSubmit={handleSubmit}
  title="Create Meeting Template"
/>

// With custom configuration
<MeetingTemplateModal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  onSubmit={handleSubmit}
  initialData={existingData}
  mode="edit"
  config={{
    showDatetime: false,
    showLocation: false,
    readOnly: false,
  }}
  permissions={{
    MODIFY_MEETING: true,
    INVITE_OTHERS: true,
  }}
/>

// With custom API function
<MeetingTemplateModal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  apiFunc={customApiFunction}
  transform={customTransformFunction}
  validationSchema={customValidationSchema}
/>`}
        </pre>
      </div>
    </div>
  );
};

export default MeetingTemplateModalExample;

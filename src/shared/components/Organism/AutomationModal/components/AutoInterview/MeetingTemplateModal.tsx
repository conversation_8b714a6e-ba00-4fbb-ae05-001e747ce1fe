import React, { useMemo, useState } from 'react';
import { motion } from 'framer-motion';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Form from 'shared/uikit/Form';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import classes from './MeetingTemplateModal.module.scss';
import useMeetingTemplateForm from './useMeetingTemplateForm';
import useMeetingTemplateFormFields from './useMeetingTemplateFormFields';
import type { MeetingTemplateModalProps } from './types';
import TemplateSelection from '@shared/components/Organism/TemplateSelectionInterface/TemplateSelectionInterface';

type MeetingTemplateModalContentProps = {
  onClose: () => void;
  submitButtonText: string;
  isLoading?: boolean;
  permissions?: Record<string, boolean>;
  targetAttendee?: any;
  config?: any;
  setShowTemplate: (value: boolean) => void;
};

const MeetingTemplateModalContent: React.FC<
  MeetingTemplateModalContentProps
> = ({
  onClose,
  submitButtonText,
  isLoading,
  permissions,
  targetAttendee,
  config,
  setShowTemplate,
}) => {
  const { t } = useTranslation();

  const { formFields } = useMeetingTemplateFormFields(
    {
      permissions,
      targetAttendee,
      config,
    },
    () => setShowTemplate(true)
  );

  const handleClose = () => {
    onClose?.();
  };

  return (
    <FixedRightSideModalDialog
      wide
      onClose={handleClose}
      onBack={handleClose}
      onClickOutside={handleClose}
    >
      <ModalHeaderSimple
        title={t('meeting_template')}
        backButtonProps={{
          onClick: handleClose,
        }}
        noCloseButton
        hideBack={false}
        visibleHeaderDivider
      />

      <ModalBody className={classes.modalBody}>
        <motion.div
          id="modalMotion"
          initial="visible"
          animate="hidden"
          variants={{
            visible: { originX: 0, opacity: 0, scale: 0.95 },
            hidden: { originX: 0.5, opacity: 1, scale: 1 },
          }}
          transition={{ duration: 0.2 }}
        >
          {formFields?.length && <DynamicFormBuilder groups={formFields} />}
        </motion.div>
      </ModalBody>

      <ModalFooter>
        <Flex className="!flex !flex-row items-center gap-8">
          <Button
            onClick={handleClose}
            fullWidth
            schema="semi-transparent3"
            label={t('discard')}
            variant="default"
          />
          {/* <Flex className={classes.divider} /> */}
          <SubmitButton
            fullWidth
            label={submitButtonText}
            isLoading={isLoading}
            variant="default"
          />
        </Flex>
      </ModalFooter>
    </FixedRightSideModalDialog>
  );
};

const MeetingTemplateModal: React.FC<MeetingTemplateModalProps> = ({
  isOpen = true,
  onClose,
  onSubmit,
  onSuccess,
  initialData,
  title,
  isLoading = false,
  permissions,
  targetAttendee,
  mode = 'create',
}) => {
  const { t } = useTranslation();
  const [showTemplate, setShowTemplate] = useState(false);

  const modalTitle =
    title ||
    (mode === 'create'
      ? t('create_meeting_template')
      : t('edit_meeting_template'));

  const {
    initialValues,
    validationSchema,
    onSubmit: handleFormSubmit,
  } = useMeetingTemplateForm({
    initialData,
    onSubmit,
    onSuccess,
    permissions,
    targetAttendee,
  });

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  if (!isOpen) {
    return null;
  }

  if (showTemplate) {
    return <TemplateSelection onCloseTemplate={() => setShowTemplate(false)} />;
  }

  return (
    <Form
      initialValues={initialValues}
      validationSchema={validationSchema}
      apiFunc={handleFormSubmit}
      onSuccess={onSuccess}
      enableReinitialize
    >
      <MeetingTemplateModalContent
        onClose={handleClose}
        submitButtonText={t('update')}
        isLoading={isLoading}
        permissions={permissions}
        targetAttendee={targetAttendee}
        setShowTemplate={setShowTemplate}
      />
    </Form>
  );
};

export default MeetingTemplateModal;

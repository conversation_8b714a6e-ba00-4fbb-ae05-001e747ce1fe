import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { useFormikContext } from 'formik';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Form from 'shared/uikit/Form';
import Button from 'shared/uikit/Button';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Flex from 'shared/uikit/Flex';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useMeetingTemplateForm from './useMeetingTemplateForm';
import useMeetingTemplateFormFields from './useMeetingTemplateFormFields';
import type {
  MeetingTemplateModalProps,
  MeetingTemplateFormData,
} from './types';
import classes from './MeetingTemplateModal.module.scss';

const MeetingTemplateModalContent: React.FC<{
  onClose: () => void;
  onBack?: () => void;
  title: string;
  submitButtonText: string;
  isLoading?: boolean;
  permissions?: Record<string, boolean>;
  targetAttendee?: any;
  config?: any;
}> = ({
  onClose,
  onBack,
  title,
  submitButtonText,
  isLoading,
  permissions,
  targetAttendee,
  config,
}) => {
  const { t } = useTranslation();
  const formContext = useFormikContext<MeetingTemplateFormData>();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
    styles: {
      container: isBusinessApp
        ? classes.wideConfirmationModalContent
        : classes.confirmationModalContent,
      wrapper: isBusinessApp
        ? classes.wideConfirmationModalContent
        : classes.confirmationModalContent,
    },
  });

  const { formFields } = useMeetingTemplateFormFields({
    permissions,
    targetAttendee,
    config,
  });

  console.log(
    'formFields',
    formFields?.map((item) => item?.cp)
  );

  const handleClose = () => {
    if (formContext.dirty) {
      openConfirmDialog({
        title: t('confirm_title'),
        message: t('confirm_desc'),
        cancelButtonText: t('confirm_cancel'),
        confirmButtonText: t('confirm_ok'),
        cancelCallback: onClose,
        isReverse: true,
      });
    } else {
      onClose();
    }
  };

  const handleBack = () => {
    if (onBack) {
      if (formContext.dirty) {
        openConfirmDialog({
          title: t('confirm_title'),
          message: t('confirm_desc'),
          cancelButtonText: t('confirm_cancel'),
          confirmButtonText: t('confirm_ok'),
          cancelCallback: onBack,
          isReverse: true,
        });
      } else {
        onBack();
      }
    } else {
      handleClose();
    }
  };

  return (
    <FixedRightSideModalDialog
      wide={isBusinessApp}
      onClose={handleClose}
      onBack={handleBack}
      onClickOutside={handleClose}
    >
      <ModalHeaderSimple
        title={title}
        backButtonProps={{
          onClick: handleBack,
          className: classes.showBack,
        }}
        closeButtonProps={{
          className: classes.showBack,
        }}
        hideBack={!onBack}
        visibleHeaderDivider
        className={classes.largeModalHeader}
      />

      <ModalBody className={classes.modalBody}>
        <motion.div
          id="modalMotion"
          initial="visible"
          animate="hidden"
          variants={{
            visible: { originX: 0, opacity: 0, scale: 0.95 },
            hidden: { originX: 0.5, opacity: 1, scale: 1 },
          }}
          transition={{ duration: 0.2 }}
        >
          {formFields?.length && <DynamicFormBuilder groups={formFields} />}
        </motion.div>
      </ModalBody>

      <ModalFooter className={classes.footer}>
        <Button
          onClick={handleClose}
          fullWidth
          schema="semi-transparent3"
          label={t('discard')}
        />
        <Flex className={classes.divider} />
        <SubmitButton
          fullWidth
          label={submitButtonText}
          isLoading={isLoading}
        />
      </ModalFooter>
    </FixedRightSideModalDialog>
  );
};

const MeetingTemplateModal: React.FC<MeetingTemplateModalProps> = ({
  isOpen = true,
  onClose,
  onSubmit,
  onSuccess,
  initialData,
  title,
  submitButtonText,
  isLoading = false,
  wide = true,
  permissions,
  targetAttendee,
  mode = 'create',
}) => {
  const { t } = useTranslation();

  const modalTitle =
    title ||
    (mode === 'create'
      ? t('create_meeting_template')
      : t('edit_meeting_template'));
  const buttonText =
    submitButtonText || (mode === 'create' ? t('create') : t('update'));

  const {
    initialValues,
    validationSchema,
    onSubmit: handleFormSubmit,
  } = useMeetingTemplateForm({
    initialData,
    onSubmit,
    onSuccess,
    permissions,
    targetAttendee,
  });

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Form
      initialValues={initialValues}
      validationSchema={validationSchema}
      apiFunc={handleFormSubmit}
      onSuccess={onSuccess}
      enableReinitialize
    >
      <MeetingTemplateModalContent
        onClose={handleClose}
        title={modalTitle}
        submitButtonText={buttonText}
        isLoading={isLoading}
        permissions={permissions}
        targetAttendee={targetAttendee}
      />
    </Form>
  );
};

export default MeetingTemplateModal;

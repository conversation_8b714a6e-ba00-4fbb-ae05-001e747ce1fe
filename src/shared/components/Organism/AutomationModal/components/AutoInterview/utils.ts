import { MeetingChannel } from 'shared/types/schedules/schedules';
import { Time } from 'shared/utils/Time';
import type {
  MeetingTemplateFormData,
  MeetingTemplateApiResponse,
} from './types';

export const validateMeetingTemplateForm = (
  data: Partial<MeetingTemplateFormData>,
  config: { requireDateTime?: boolean; requireLocation?: boolean } = {}
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.title?.trim()) {
    errors.push('Title is required');
  }

  if (config.requireDateTime) {
    if (!data.startDate) {
      errors.push('Start date is required');
    }
    if (!data.startTime?.value) {
      errors.push('Start time is required');
    }
  }

  if (config.requireLocation && data.contactType?.value === 'ON_SITE') {
    if (!data.location && !data.room) {
      errors.push('Location or room is required for on-site meetings');
    }
  }

  if (
    data.meetingChannel === MeetingChannel.CUSTOM_LINK &&
    !data.customLink?.trim()
  ) {
    errors.push('Custom link is required when using custom meeting channel');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const transformMeetingTemplateToApi = (
  data: MeetingTemplateFormData
): Partial<MeetingTemplateApiResponse> => ({
  title: data.title,
  description: data.description || '',
  duration: data.duration?.value,
  contactType: data.contactType?.value,
  meetingChannel: data.meetingChannel,
  remind: data.remind?.value,
  attendees: data.attendees || [],
  attachmentFileIds: data.attachmentFileIds || [],
});

export const transformApiToMeetingTemplate = (
  apiData: MeetingTemplateApiResponse
): Partial<MeetingTemplateFormData> => ({
  title: apiData.title,
  description: apiData.description,
  duration: apiData.duration
    ? { label: apiData.duration, value: apiData.duration }
    : undefined,
  contactType: apiData.contactType
    ? { label: apiData.contactType, value: apiData.contactType }
    : undefined,
  meetingChannel: apiData.meetingChannel,
  remind: apiData.remind
    ? { label: apiData.remind, value: apiData.remind }
    : undefined,
  attendees: apiData.attendees,
  attachmentFileIds: apiData.attachmentFileIds,
});

export const generateDefaultMeetingTitle = (prefix = 'Meeting'): string => {
  const now = new Date();
  const dateStr = now.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
  });
  const timeStr = now.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  return `${prefix} - ${dateStr} at ${timeStr}`;
};

export const calculateMeetingEndTime = (
  startDate: Date | string,
  startTime: string,
  durationMinutes: number
): { endDate: Date | string; endTime: string } => {
  const dateStr =
    typeof startDate === 'string'
      ? startDate
      : startDate.toISOString().split('T')[0];
  const startDateTime = Time.getUTCTimeByFormDateAndTime(dateStr, startTime);
  const endDateTime = startDateTime.add(durationMinutes, 'minutes');

  return {
    endDate: endDateTime.format('YYYY-MM-DD'),
    endTime: endDateTime.format('HH:mm'),
  };
};

export const isMeetingInPast = (
  startDate: Date | string,
  startTime: string
): boolean => {
  try {
    const dateStr =
      typeof startDate === 'string'
        ? startDate
        : startDate.toISOString().split('T')[0];
    const meetingDateTime = Time.getUTCTimeByFormDateAndTime(
      dateStr,
      startTime
    );

    return Time.isBeforeNow(meetingDateTime);
  } catch {
    return false;
  }
};

export const formatMeetingDuration = (durationValue: string): string => {
  const durationMap: Record<string, string> = {
    _15_MINUTES: '15 minutes',
    _30_MINUTES: '30 minutes',
    _60_MINUTES: '1 hour',
    _120_MINUTES: '2 hours',
  };

  return durationMap[durationValue] || durationValue;
};

export const formatMeetingReminder = (reminderValue: string): string => {
  const reminderMap: Record<string, string> = {
    _5_MIN_BEFORE: '5 minutes before',
    _10_MIN_BEFORE: '10 minutes before',
    _15_MIN_BEFORE: '15 minutes before',
    _30_MIN_BEFORE: '30 minutes before',
    _1_HOUR_BEFORE: '1 hour before',
    _1_DAY_BEFORE: '1 day before',
  };

  return reminderMap[reminderValue] || reminderValue;
};

export const sanitizeMeetingTemplateData = (
  data: MeetingTemplateFormData
): MeetingTemplateFormData => ({
  ...data,
  title: data.title?.trim() || '',
  description: data.description?.trim() || '',
  customLink: data.customLink?.trim() || '',
  attendees: data.attendees?.filter(Boolean) || [],
  attachmentFileIds: data.attachmentFileIds?.filter(Boolean) || [],
});

export const cloneMeetingTemplateData = (
  data: MeetingTemplateFormData
): MeetingTemplateFormData => JSON.parse(JSON.stringify(data));

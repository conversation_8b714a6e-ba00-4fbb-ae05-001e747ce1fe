@import '/src/shared/theme/theme.scss';

@layer organism {
  .largeModalHeader {
    border-bottom: 1px solid colors(techGray_20);
    padding: variables(gutter);
  }

  .footer {
    flex-direction: row;
    border-top: 1px solid colors(techGray_20);
    padding: variables(gutter);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .done {
    min-width: 80px;
  }

  .countText {
    flex: 1;
  }

  .listWrapper {
    min-height: 300px;
    overflow-y: auto;
    max-height: calc(100vh - 112px);
  }

  .noXPadding {
    padding-left: 0;
    padding-right: 0;
  }

  @media (min-width: breakpoints(tablet)) {
    .footer {
      padding-top: variables(largeGutter);
    }
  }
}

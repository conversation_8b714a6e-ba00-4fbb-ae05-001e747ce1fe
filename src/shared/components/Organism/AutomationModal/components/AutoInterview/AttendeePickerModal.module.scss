@import '/src/shared/theme/theme.scss';

@layer organism {
  .footer {
    flex-direction: row;
    border-top: 1px solid colors(techGray_10);
  }
  .done {
    padding: 7px 24.5px;
    margin-left: auto;
  }
  .countText {
    margin: auto 0;
  }
  .listWrapper {
    max-height: unset !important;
    height: unset;
  }

  .noXPadding {
    padding-left: 0;
    padding-right: 0;
  }

  @media (min-width: breakpoints(tablet)) {
    .footer {
      padding-top: variables(largeGutter);
    }
  }
}

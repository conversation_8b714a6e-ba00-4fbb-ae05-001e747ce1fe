import { useFormikContext } from 'formik';
import React, { useCallback, useMemo } from 'react';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import AttendeePicker from 'shared/components/Organism/ScheduleCreationForm/partials/AttendeePicker.component';
import { useMeetingFormFields } from 'shared/hooks/schedules/useMeetingFormFields';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Divider from 'shared/uikit/Divider';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import cnj from 'shared/uikit/utils/cnj';
import useMedia from 'shared/uikit/utils/useMedia';
import geoApi from 'shared/utils/api/geo';
import { schedulesDb } from 'shared/utils/constants/enums';
import { genTimeApiFunction } from 'shared/utils/form/form.util';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './useMeetingTemplateFormFields.module.scss';
import type {
  MeetingTemplateFormData,
  UseMeetingTemplateFormFieldsOptions,
  MeetingTemplateFormFieldGroup,
} from './types';

const useMeetingTemplateFormFields = (
  options: UseMeetingTemplateFormFieldsOptions = {}
) => {
  const { permissions, targetAttendee, config = {} } = options;
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { authUser } = useGetAppObject();
  const { values, setFieldValue } = useFormikContext<MeetingTemplateFormData>();

  const isRemote = useMemo(
    () => values?.contactType?.value === schedulesDb.contactType[0].value,
    [values?.contactType?.value]
  );

  const {
    attachments,
    attendeePermissions,
    description,
    meetingModel,
    meetingRemind,
    room,
  } = useMeetingFormFields(permissions as any);

  const invalidTimeFormat = useMemo(
    () => ({
      label: t('invalid_time_format'),
      value: '',
      noClick: true,
    }),
    [t]
  );

  const eventTitle = useCallback(
    (label: string): MeetingTemplateFormFieldGroup => ({
      label,
      required: true,
      name: 'title',
      cp: 'input',
      wrapStyle: classes.formItem,
      maxLength: 100,
      isFocused: true,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [permissions]
  );

  const meetingDate = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      name: 'startDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.leftItem, classes.formItem),
      containerProps: {
        className: classes.datePickerRight,
      },
      variant: 'input',
      required: true,
      picker: 'date',
      containerWidth: isMoreThanTablet ? 360 : undefined,
      label: t('date'),
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, isMoreThanTablet, permissions]
  );

  const meetingTime = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      name: 'startTime',
      wrapStyle: cnj(classes.rightItem, classes.formItem),
      label: t('time'),
      required: true,
      doNotUseTranslation: true,
      rightIconProps: {
        name: 'clock',
      },
      maxLength: 5,
      visibleRightIcon: true,
      cp: 'asyncAutoComplete',
      checkIsValid: (value: string) => /^(\d{0,2}(:\d{0,2})?)?$/.test(value),
      showDropDownWithoutEnteringAnything: true,
      initSearchValue: '',
      apiFunc: genTimeApiFunction({
        options: schedulesDb.timeOptions,
        invalidOption: invalidTimeFormat,
      }),
      showPreview: true,
      onChange: (value: any, { field }: any) => {
        if (field?.value?.label?.length === 1 && value?.label?.length === 2) {
          setTimeout(() => {
            setFieldValue('startTime', {
              value: null,
              label: value?.label?.concat(':'),
            });
          });
        }
      },
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [invalidTimeFormat, setFieldValue, t, permissions]
  );

  const meetingDuration = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      name: 'duration',
      cp: 'dropdownSelect',
      label: t('meeting_duration'),
      wrapStyle: classes.formItem,
      options: schedulesDb.meetingDurationOptions,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, permissions]
  );

  const location = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      name: 'location',
      cp: 'cityPicker',
      apiParams: {
        countryCode: authUser?.location?.countryCode,
      },
      wrapStyle: classes.formItem,
      noExplicitEditButton: true,
      classNames: {
        itemWrapper: classes.noBottomMargin,
      },
      rightSideProps: {
        isFromRightSide: true,
        modalComponent: FixedRightSideModalDialog,
        modalComponentProps: {
          wide: isBusinessApp,
        },
        inputLabel: t('city'),
      },
      hideBack: false,
      hideClose: true,
      visibleHeaderBorderBottom: true,
      visibleRequiredHint: false,
      styles: {
        formRoot: classes.locationFormRoot,
        formWrap: classes.locationFormRootFields,
        submitButton: classes.maxWidth,
      },
      primaryAction: {
        label: t('add_location'),
      },
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, authUser?.location?.countryCode, permissions]
  );

  const roomGroup = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      ...room,
      wrapStyle: classes.formItem,
    }),
    [room]
  );

  const timeZone = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      label: t('time_zone'),
      wrapStyle: classes.formItem,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,

      name: 'timezone',
      cp: 'asyncAutoComplete',
      apiFunc: geoApi.searchTimezone,
      visibleRightIcon: true,
      rightIconProps: {
        name: 'search',
      },
      required: true,
    }),
    [t, permissions]
  );

  const descriptionGroup = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      ...description,
      wrapStyle: classes.formItem,
      className: classes.description,
    }),
    [description]
  );

  const meetingModelGroup = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      ...meetingModel,
      label: t('meeting_type'),
      wrapStyle: classes.formItem,
    }),
    [meetingModel, t]
  );

  const meetingRemindGroup = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      ...meetingRemind,
      wrapStyle: classes.formItem,
    }),
    [meetingRemind]
  );

  const attendees = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      name: 'attendees',
      cp: (props: any) => (
        <AttendeePicker
          leftLabel={t('attendees')}
          buttonLabel={t('add_attendees')}
          creator={values?.creator || authUser}
          target={targetAttendee}
          permissionValue={schedulesDb.permissions.MODIFY_MEETING}
          {...props}
          className="mt-20"
        />
      ),
    }),
    [t, values?.creator, authUser, targetAttendee]
  );

  const meetingChannel = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('meeting_channel'),
        className: classes.formGroupHeader,
      },
      classNames: {
        itemWrapper: classes.checkboxContainer,
      },
      wrapStyle: 'mt-20',
      className: classes.checkboxGroup,
      cp: 'radioGroup',
      name: 'meetingChannel',
      options: [
        {
          label: t('lobox_account'),
          value: 'LOBOX_ACCOUNT',
        },
        {
          label: t('personal_account'),
          value: 'PERSONAL_ACCOUNT',
        },
        {
          label: t('cr_meet_via_custom_link'),
          value: 'CUSTOM_LINK',
          children: (
            <div className="mt-12">
              <input
                type="text"
                name="customLink"
                placeholder={t('meeting_link')}
                className="w-full p-3 border border-gray-300 rounded-md"
                value={values?.customLink || ''}
                onChange={(e) => setFieldValue('customLink', e.target.value)}
              />
            </div>
          ),
        },
      ],
      label: t('meeting_channel'),
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, values?.customLink, setFieldValue, permissions]
  );

  const divider = useMemo(
    (): MeetingTemplateFormFieldGroup => ({
      cp: () => <Divider className={classes.divider} />,
      name: 'divider',
    }),
    []
  );

  const formFields = useMemo(() => {
    const fields: MeetingTemplateFormFieldGroup[] = [
      eventTitle(t('meeting_title')),
      meetingModelGroup,
    ];

    if (config.showLocation !== false && !isRemote) {
      fields.push(location, roomGroup);
    }

    if (config.showDuration !== false) {
      fields.push(meetingDuration);
    }

    fields.push(meetingRemindGroup, descriptionGroup);

    if (config.showAttendees !== false) {
      fields.push(attendees);
    }

    if (config.showAttachments !== false) {
      fields.push(meetingChannel, attachments);
    }

    if (config.showPermissions !== false) {
      fields.push(attendeePermissions);
    }

    return fields.filter(Boolean);
  }, [
    eventTitle,
    t,
    config,
    timeZone,
    targetAttendee,
    meetingModelGroup,
    isRemote,
    location,
    roomGroup,
    meetingDuration,
    meetingRemindGroup,
    descriptionGroup,
    attendees,
    meetingChannel,
    attachments,
    attendeePermissions,
  ]);

  return {
    formFields,
    eventTitle,
    meetingDate,
    meetingTime,
    meetingDuration,
    location,
    roomGroup,
    timeZone,
    descriptionGroup,
    meetingModelGroup,
    meetingRemindGroup,
    attendees,
    meetingChannel,
    attachments,
    attendeePermissions,
    divider,
  };
};

export default useMeetingTemplateFormFields;

import { useParams } from 'next/navigation';
import React, { useMemo, useState } from 'react';
import EnhancedTemplateForm from '@shared/components/Organism/AutomationModal/components/TemplateForm/EnhancedTemplateForm';
import TemplateList from '@shared/components/Organism/AutomationModal/components/TemplateForm/TemplateList';
import { useTemplateActions } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateActions';
import { useTemplateForm } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateForm';
import { useTemplateList } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateList';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import {
  deletePipelineAutoInterview,
  getPipelineAutoInterview,
  putPipelineAutoInterview,
} from '@shared/utils/api/pipeline';
import { QueryKeys } from '@shared/utils/constants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  NormalizedTemplate,
  TemplateAction,
} from '@shared/components/Organism/AutomationModal/types/template.types';
import MeetingTemplateModal from './MeetingTemplateModal';

const AutoInterviewModal: React.FC = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const [showForm, setShowForm] = useState(false);
  const { handleSuccess } = useResponseToast();
  const params = useParams();
  const pipelineId = Number((automationState?.data as any)?.id);

  const {
    data: interview,
    isLoading: isLoadingInterview,
    refetch: refetchAutoInterview,
  } = useReactQuery({
    action: {
      apiFunc: () => getPipelineAutoInterview(pipelineId),
      key: [QueryKeys.getPipelineAutoInterview, pipelineId],
    },
  });

  const {
    mutate: putPipelineAutoInterviewMutation,
    isPending: isUpdatingPending,
  } = useReactMutation({
    apiFunc: putPipelineAutoInterview,
    onSuccess: () => {
      refetchAutoInterview();
    },
  });

  const {
    mutate: deletePipelineAutoInterviewMutation,
    isPending: isDeletePending,
  } = useReactMutation({
    apiFunc: deletePipelineAutoInterview,
    onSuccess: () => {
      refetchAutoInterview();
    },
  });

  const templateForm = useTemplateForm({
    onFormChange: (_values, _isValid) => {},
    onSubmit: (data, isCreate) => {
      if (isCreate) {
        templateActions.createTemplate(data);
      } else if (templateForm.editingTemplate) {
        templateActions.updateTemplate(
          parseInt(templateForm.editingTemplate.id, 10),
          data,
          () => {
            handleSuccess({
              message: t('auto_interview_created_message'),
              title: t('auto_interview_created_title'),
            });
          }
        );
      }
    },
  });

  const templateList = useTemplateList({
    searchEnabled: true,
    onSuccess: (template) => {
      if (template?.length) {
        templateForm.startEditing(
          template?.find(
            (item) => item?.id === templateForm?.editingTemplate?.id
          ) as unknown as NormalizedTemplate
        );
      }
    },
  });

  const isDefaultTemplateLoading =
    isDeletePending || isUpdatingPending || isLoadingInterview;

  const templateActions = useTemplateActions(
    {
      onTemplateCreated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateUpdated: () => {
        templateList.refetch();
        setShowForm(false);
      },
      onTemplateDeleted: () => {
        templateList.refetch();
      },
      onDefaultChanged: (templateId, isCurrentlyDefault) => {
        templateList.refetch();

        if (isCurrentlyDefault) {
          deletePipelineAutoInterviewMutation(pipelineId);
        } else {
          putPipelineAutoInterviewMutation({
            pipelineId,
            body: {
              templateId: Number(templateId),
            },
          });
        }
      },
    },
    {
      message: t('auto_reply_duplicated_message'),
      title: t('auto_reply_duplicated_title'),
    }
  );

  const templateActionsList: TemplateAction[] = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.duplicateTemplate(template);
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template: NormalizedTemplate) => {
        templateForm.startEditing(template);
        setShowForm(true);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template: NormalizedTemplate) => {
        templateActions.deleteTemplate(template);
      },
    },
  ];

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const handleCreate = () => {
    templateForm.startCreating();
    setShowForm(true);
  };

  const handleTemplateClick = (templateId: string) => {
    const template = templateList.getTemplateById(templateId);
    if (template) {
      templateForm.startEditing(template);
      setShowForm(true);
    }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    templateForm.resetForm();
  };

  const handleSetDefault = (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    templateActions.setDefaultTemplate(templateId, isCurrentlyDefault);
  };

  const isDefault = useMemo(
    () =>
      !!templateForm?.editingTemplate?.default &&
      !!interview?.templateId &&
      !!templateForm?.editingTemplate?.id &&
      templateForm?.editingTemplate?.id === interview?.templateId,
    [
      templateForm?.editingTemplate?.default,
      templateForm?.editingTemplate?.id,
      interview?.templateId,
    ]
  );

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={t('interview_templates')}
        backButtonProps={{
          onClick: showForm ? handleFormDiscard : handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody className="p-6 h-full overflow-auto mb-8">
        {showForm ? (
          <MeetingTemplateModal
            isOpen
            onClose={() => setShowForm(false)}
            onSubmit={(submit) => console.log('submit', submit)}
            title="Create Meeting Template"
            mode="create"
            wide
          />
        ) : (
          // <EnhancedTemplateForm
          //   isDefaultTemplate={isDefault}
          //   onSetDefault={() =>
          //     templateForm?.editingTemplate?.id &&
          //     templateActions.setDefaultTemplate(
          //       templateForm?.editingTemplate?.id,
          //       templateForm?.editingTemplate?.default
          //     )
          //   }
          //   isUpdatingDefault={
          //     (templateForm?.editingTemplate?.id &&
          //       templateActions.isUpdatingDefault ===
          //         templateForm.editingTemplate.id) ||
          //     templateActions?.isMutating
          //   }
          //   formData={templateForm.formData}
          //   onSubmit={templateForm.handleSubmit}
          //   isLoading={templateForm.isLoading}
          //   onChange={templateForm.handleFormChange}
          //   config={{
          //     showDelay: true,
          //     showFollowup: true,
          //     showAttachments: true,
          //   }}
          // />
          <TemplateList
            templates={templateList.templates}
            isLoading={templateList.isLoading}
            searchQuery={templateList.searchQuery}
            defaultTemplateId={interview?.templateId}
            onSearchChange={templateList.handleSearchChange}
            onTemplateClick={handleTemplateClick}
            onSetDefault={handleSetDefault}
            actions={templateActionsList}
            isUpdatingDefault={templateActions.isUpdatingDefault}
            isDefaultTemplateLoading={isDefaultTemplateLoading}
            config={{
              showSearch: true,
              showActions: true,
              showDefaultToggle: true,
            }}
          />
        )}
      </ModalBody>

      <ModalFooter>
        {showForm ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={templateForm.isEditing ? t('update') : t('create')}
              schema="primary-blue"
              variant="default"
              onClick={() => templateForm.handleSubmit()}
              className="flex-1"
              disabled={!templateForm.canSubmit}
              isLoading={templateForm.isLoading || templateActions.isMutating}
            />
          </Flex>
        ) : (
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={handleCreate}
          />
        )}
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default AutoInterviewModal;

@import '/src/shared/theme/theme.scss';

@layer organism {
  .attendeePickerRoot {
    color: colors(brand);
    flex-direction: column;
    gap: variables(gutter) * 0.5;
  }

  .titleWrapper {
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: variables(gutter) * 0.5;
  }

  .usersWrapper {
    gap: variables(gutter) * 0.5;
    flex-direction: column;
  }

  .userWrapper {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

import { motion } from 'framer-motion';
import React, { useState } from 'react';
import Button from 'shared/uikit/Button';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import Typography from 'shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './AttendeePickerModal.module.scss';
import type { UserType } from 'shared/types/user';

interface AttendeePickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (selectedUsers: UserType[]) => void;
  title: string;
  maxAttendees?: number;
  currentAttendees?: UserType[];
}

const AttendeePickerModal: React.FC<AttendeePickerModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  maxAttendees = 100,
  currentAttendees = [],
}) => {
  const { t } = useTranslation();
  const [selectedUsers, setSelectedUsers] = useState<UserType[]>([]);

  const handleSubmit = () => {
    onSubmit(selectedUsers);
    setSelectedUsers([]);
  };

  const handleClose = () => {
    setSelectedUsers([]);
    onClose();
  };

  if (!isOpen) {
    return null;
  }

  const groups = [
    {
      name: 'attendees',
      cp: 'userPicker',
      required: true,
      placeholder: t('search'),
      isMultiSelect: true,
      hideCounter: true,
      max: maxAttendees,
      classNames: {
        listWrapper: classes.listWrapper,
      },
      userType: 'PERSON',
      detectEmails: true,
      value: selectedUsers,
      onChange: (users: UserType[]) => setSelectedUsers(users),
    },
  ];

  const totalCount = currentAttendees.length + selectedUsers.length;

  return (
    <FixedRightSideModalDialog
      onBack={handleClose}
      onClickOutside={handleClose}
      wide
    >
      <ModalHeaderSimple
        visibleHeaderDivider
        title={title}
        className={classes.largeModalHeader}
        hideBack={false}
        noCloseButton
      />
      <ModalBody className={classes.noXPadding}>
        <motion.div
          id="modalMotion"
          initial="visible"
          animate="hidden"
          variants={{
            visible: { originX: 0, opacity: 0, scale: 0.95 },
            hidden: { originX: 0.5, opacity: 1, scale: 1 },
          }}
          transition={{ duration: 0.2 }}
        >
          <DynamicFormBuilder groups={groups} />
        </motion.div>
      </ModalBody>
      <ModalFooter className={classes.footer}>
        <Typography
          className={classes.countText}
          color="gray"
          font="400"
          size={13}
          height={15}
        >
          {totalCount}/{maxAttendees}
        </Typography>
        <Button
          className={classes.done}
          onClick={handleSubmit}
          label={t('done')}
        />
      </ModalFooter>
    </FixedRightSideModalDialog>
  );
};

export default AttendeePickerModal;

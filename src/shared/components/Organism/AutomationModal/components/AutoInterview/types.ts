import type { UserType } from '@shared/types/user';
import type {
  MeetingDuration,
  MeetingReminderDuration,
  MeetingChannel,
  ContactType,
  MeetingDatetimeType,
} from 'shared/types/schedules/schedules';
import type { UploadedFile } from 'shared/uikit/AttachmentPicker/AttachmentPicker.component';

export interface MeetingTemplateFormData {
  title: string;
  description: string;

  startDate?: Date | string;
  startTime?: { label: string; value: string };
  endDate?: Date | string;
  endTime?: { label: string; value: string };
  duration?: { label: string; value: MeetingDuration };
  timezone?: { label: string; value: string; offset?: number };

  contactType?: { label: string; value: ContactType };
  meetingChannel?: MeetingChannel;
  customLink?: string;
  location?: any;
  room?: any;
  remind?: { label: string; value: MeetingReminderDuration };

  attendees?: UserType[];
  attendeePermissions?: Array<{ label: string; value: string }>;
  permissions?: Record<string, boolean>;
  creator?: UserType;
  currentUserIsCreator?: boolean;
  targetAttendee?: UserType;

  attachmentFileIds?: string[];
  attachmentFiles?: UploadedFile[];

  datetimeType?: MeetingDatetimeType;
  allDay?: boolean;
}

export interface MeetingTemplateModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onSubmit?: (data: MeetingTemplateFormData) => void;
  onSuccess?: (data: any) => void;
  initialData?: Partial<MeetingTemplateFormData>;
  title?: string;
  submitButtonText?: string;
  isLoading?: boolean;
  wide?: boolean;
  permissions?: Record<string, boolean>;
  targetAttendee?: UserType;
  mode?: 'create' | 'edit';
}

export interface MeetingTemplateFormConfig {
  showDatetime?: boolean;
  showDuration?: boolean;
  showLocation?: boolean;
  showAttendees?: boolean;
  showAttachments?: boolean;
  showPermissions?: boolean;
  showAdvancedSettings?: boolean;
  readOnly?: boolean;
  requiredFields?: string[];
}

export interface UseMeetingTemplateFormOptions {
  initialData?: Partial<MeetingTemplateFormData>;
  onSubmit?: (data: MeetingTemplateFormData) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
  config?: MeetingTemplateFormConfig;
  permissions?: Record<string, boolean>;
  targetAttendee?: UserType;
  apiFunc?: (data: any) => Promise<any>;
  transform?: (data: MeetingTemplateFormData) => any;
  validationSchema?: any;
}

export interface UseMeetingTemplateFormFieldsOptions {
  permissions?: Record<string, boolean>;
  targetAttendee?: UserType;
  config?: MeetingTemplateFormConfig;
}

// Default initial values for the form
export const DEFAULT_MEETING_TEMPLATE_VALUES: Partial<MeetingTemplateFormData> =
  {
    title: '',
    description: '',
    attendees: [],
    attendeePermissions: [],
    attachmentFileIds: [],
    currentUserIsCreator: true,
    allDay: false,
  };

// Form field group types
export type MeetingTemplateFormFieldGroup = {
  name: string;
  cp: string | React.ComponentType<any>;
  label?: string;
  required?: boolean;
  wrapStyle?: string;
  className?: string;
  [key: string]: any;
};

export interface MeetingTemplateFormErrors {
  title?: string;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  customLink?: string;
  [key: string]: string | undefined;
}

export interface MeetingTemplateApiResponse {
  id: string;
  title: string;
  description: string;
  duration: MeetingDuration;
  contactType: ContactType;
  meetingChannel: MeetingChannel;
  remind: MeetingReminderDuration;
  attendees: UserType[];
  attachmentFileIds: string[];
  createdAt: string;
  updatedAt: string;
}

export type MeetingTemplateTransformFunction = (
  data: MeetingTemplateFormData
) => any;

export type MeetingTemplateSuccessHandler = (
  response: any,
  formData: MeetingTemplateFormData
) => void;

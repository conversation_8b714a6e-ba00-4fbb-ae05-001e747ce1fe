import React from 'react';
import AvatarCard from 'shared/uikit/AvatarCard';
import type { UserType } from 'shared/types/user';

type Props = {
  role?: string;
  user: Pick<UserType, 'fullName' | 'usernameAtSign' | 'croppedImageUrl'>;
};

const UserItem = ({ user, role }: Props) => (
  <AvatarCard
    noHover
    data={{
      title: user.fullName,
      subTitle: user.usernameAtSign,
      image: user.croppedImageUrl,
      role,
    }}
    avatarProps={{
      isCompany: false,
    }}
    withPadding={false}
  />
);

export default UserItem;

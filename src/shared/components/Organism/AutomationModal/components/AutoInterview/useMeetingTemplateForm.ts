import { useMemo } from 'react';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useGetCurrentTimeZone from 'shared/hooks/api-hook/useGetCurrentTimeZone';
import useToast from 'shared/uikit/Toast/useToast';
import { schedulesDb } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  meetingDurations,
  meetingReminders,
} from 'shared/utils/normalizers/schedules';
import { Time } from 'shared/utils/Time';
import { MeetingChannel } from 'shared/types/schedules/schedules';
import { meetingCreatorAttendeePermissions } from 'shared/constants/schedules';
import formValidator, {
  isEmptyValidator,
  timeValidator,
} from 'shared/utils/form/formValidator';
import type {
  MeetingTemplateFormData,
  UseMeetingTemplateFormOptions,
} from './types';
import { DEFAULT_MEETING_TEMPLATE_VALUES } from './types';

const useMeetingTemplateForm = (
  options: UseMeetingTemplateFormOptions = {}
) => {
  const {
    initialData,
    onSubmit,
    onSuccess,
    onError,
    config = {},
    permissions,
    targetAttendee,
    apiFunc,
    transform: customTransform,
    validationSchema: customValidationSchema,
  } = options;

  const { t } = useTranslation();
  const toast = useToast();
  const { authUser } = useGetAppObject();
  const { data: currentTimeZone = {} as any } = useGetCurrentTimeZone();

  const defaultTimeZone = useMemo(
    () => ({
      ...currentTimeZone,
      value: currentTimeZone?.id,
    }),
    [currentTimeZone]
  );

  const defaultInitialValues: MeetingTemplateFormData = useMemo(
    () => ({
      ...DEFAULT_MEETING_TEMPLATE_VALUES,
      timezone: defaultTimeZone,
      contactType: schedulesDb.contactType[0],
      meetingChannel: MeetingChannel.LOBOX_ACCOUNT,
      attendeePermissions: [],
      permissions: { ...meetingCreatorAttendeePermissions },
      attachmentFileIds: [],
      attendees: [],
      duration: meetingDurations._30_MINUTES,
      remind: meetingReminders._15_MIN_BEFORE,
      creator: authUser,
      currentUserIsCreator: true,
      description: '',
      ...initialData,
    }),
    [authUser, initialData, defaultTimeZone]
  );

  const defaultTransform = useMemo(
    () => (data: MeetingTemplateFormData) => {
      const {
        duration,
        remind,
        startTime,
        timezone,
        startDate,
        attendees,
        attachmentFiles,
        attendeePermissions,
        contactType,
        location,
        meetingChannel,
        customLink,
        ...rest
      } = data;

      return {
        ...rest,
        attendees:
          attendees?.map((attendee: any) => ({
            ...(attendee.userId
              ? {
                  id: attendee.id,
                  userId: attendee.userId,
                }
              : {
                  userId: attendee.id,
                }),
            permissions:
              attendeePermissions?.map((permission: any) => permission.value) ||
              [],
          })) || [],
        duration: duration?.value,
        contactType: contactType?.value,
        remind: remind?.value,
        start:
          startDate && startTime?.value && timezone
            ? Time.getBackendDateTimeFromDateAndTime(
                startDate,
                startTime.value,
                Number(timezone.offset) || 0
              )
            : undefined,
        location: location?.value
          ? {
              id: location.value,
              name: location.label,
              countryCode: location.countryCode,
              stateCode: location.stateCode,
            }
          : null,
        meetingChannel,
        customLink:
          meetingChannel === MeetingChannel.CUSTOM_LINK ? customLink : null,
        attachmentFileIds:
          attachmentFiles?.map((file: any) => file.id) ||
          data.attachmentFileIds ||
          [],
      };
    },
    []
  );

  const defaultValidationSchema = useMemo(
    () =>
      formValidator.object().shape({
        title: isEmptyValidator('meeting_title_required'),
        startTime:
          config.showDatetime !== false
            ? timeValidator()
            : formValidator.mixed().notRequired(),
        customLink: formValidator.string().when('meetingChannel', {
          is: MeetingChannel.CUSTOM_LINK,
          then: (schema) =>
            schema.url(t('invalid_url')).required(t('custom_link_required')),
          otherwise: (schema) => schema.notRequired(),
        }),
      }),
    [t, config.showDatetime]
  );

  const defaultApiFunc = useMemo(
    () => async (data: any) => Promise.resolve({ success: true, data }),
    []
  );

  const handleSuccess = useMemo(
    () => (response: any, formData: MeetingTemplateFormData) => {
      toast({
        type: 'success',
        icon: 'check-circle',
        message: t('meeting_template_created_successfully'),
      });

      if (onSuccess) {
        onSuccess(response);
      }
    },
    [toast, t, onSuccess]
  );

  const handleError = useMemo(
    () => (error: any) => {
      toast({
        type: 'error',
        icon: 'exclamation-circle',
        message: t('meeting_template_creation_failed'),
      });

      if (onError) {
        onError(error);
      }
    },
    [toast, t, onError]
  );

  const handleSubmit = useMemo(
    () => async (data: MeetingTemplateFormData) => {
      try {
        const transformedData = customTransform
          ? customTransform(data)
          : defaultTransform(data);

        if (onSubmit) {
          await onSubmit(transformedData);
        } else if (apiFunc) {
          const response = await apiFunc(transformedData);
          handleSuccess(response, data);
        } else {
          const response = await defaultApiFunc(transformedData);
          handleSuccess(response, data);
        }
      } catch (error) {
        handleError(error);
        throw error;
      }
    },
    [
      customTransform,
      defaultTransform,
      onSubmit,
      apiFunc,
      defaultApiFunc,
      handleSuccess,
      handleError,
    ]
  );

  return {
    initialValues: defaultInitialValues,
    validationSchema: customValidationSchema || defaultValidationSchema,
    transform: customTransform || defaultTransform,
    apiFunc: apiFunc || defaultApiFunc,
    onSubmit: handleSubmit,
    onSuccess: handleSuccess,
    onError: handleError,
  };
};

export default useMeetingTemplateForm;

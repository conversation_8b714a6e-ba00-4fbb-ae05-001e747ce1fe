import { useFormikContext } from 'formik';
import { useCallback, useState } from 'react';
import Button from 'shared/uikit/Button';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './AttendeePicker.module.scss';
import type { UserType } from 'shared/types/user';
import AttendeePickerModal from './AttendeePickerModal';
import type { MeetingTemplateFormData } from './types';
import UserItem from './UserItem';

export interface AttendeeUser extends UserType {
  permissions?: string[];
}

export interface AttendeePickerProps {
  className?: string;
  leftLabel: string;
  buttonLabel: string;
  buttonIcon?: 'plus' | 'pen';
  value?: AttendeeUser[];
  creator?: AttendeeUser;
  target?: AttendeeUser;
  maxAttendees?: number;
}

const AttendeePicker: React.FC<AttendeePickerProps> = ({
  className,
  value,
  leftLabel,
  buttonLabel,
  buttonIcon = 'plus',
  creator,
  target,
  maxAttendees = 100,
}) => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { values, setFieldValue } = useFormikContext<MeetingTemplateFormData>();

  const users = Array.isArray(value) ? value : [];

  const allAttendees = [
    creator?.username ? creator : undefined,
    target?.username ? target : undefined,
    ...users,
  ].filter((a): a is AttendeeUser => !!a);

  const removeAttendee = useCallback(
    (user: AttendeeUser) => {
      const updatedAttendees =
        values?.attendees?.filter(
          (attendee: AttendeeUser) => attendee.id !== user.id
        ) || [];
      setFieldValue('attendees', updatedAttendees);
    },
    [setFieldValue, values?.attendees]
  );

  const handleAddAttendees = (selectedUsers: UserType[]) => {
    const currentAttendees = values?.attendees || [];
    const newAttendees = [...currentAttendees];

    selectedUsers.forEach((user) => {
      if (!newAttendees.find((attendee) => attendee.id === user.id)) {
        newAttendees.push(user);
      }
    });

    setFieldValue('attendees', newAttendees);
    setIsModalOpen(false);
  };

  return (
    <>
      <Flex className={cnj(classes.attendeePickerRoot, className)}>
        <Flex className={classes.titleWrapper}>
          <Typography color="smoke_coal" font="700" size={16} height={22}>
            {leftLabel}
          </Typography>
          <Typography color="border" font="500" size={13} height={15}>
            {`${allAttendees.length}/${maxAttendees}`}
          </Typography>
        </Flex>
        <Flex className={classes.usersWrapper}>
          {allAttendees.map((user) => {
            const isCreator = user?.username === creator?.username;
            const isTarget = user?.username === target?.username;

            return (
              <Flex key={user.username} className={classes.userWrapper}>
                <UserItem
                  user={user}
                  role={
                    isCreator ? t('creator') : isTarget ? t('candidate') : ''
                  }
                />
                {!isCreator && !isTarget ? (
                  <IconButton
                    colorSchema="transparentSmokeCoal"
                    name="times"
                    type="far"
                    size="md"
                    onClick={() => removeAttendee(user)}
                  />
                ) : null}
              </Flex>
            );
          })}
        </Flex>
        <Button
          className="border border-techGray_20 border-dashed mt-12 !h-[56px]"
          onClick={() => setIsModalOpen(true)}
          schema="transparent"
          label={buttonLabel}
          leftIcon={buttonIcon}
          leftType="far"
        />
      </Flex>

      <AttendeePickerModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddAttendees}
        title={t('add_attendees')}
        maxAttendees={maxAttendees}
        currentAttendees={allAttendees}
      />
    </>
  );
};

export default AttendeePicker;

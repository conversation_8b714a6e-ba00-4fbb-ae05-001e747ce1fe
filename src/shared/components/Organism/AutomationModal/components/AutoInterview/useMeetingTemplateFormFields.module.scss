// @import 'src/shared/styles/variables';
// @import 'src/shared/styles/mixins';

.formItem {
  margin-bottom: 12px;
}

.leftItem {
  @media (min-width: breakpoints(tablet)) {
    width: calc(50% - 10px);
    margin-right: 20px;
    display: inline-block;
    vertical-align: top;
  }
}

.rightItem {
  @media (min-width: breakpoints(tablet)) {
    width: calc(50% - 10px);
    display: inline-block;
    vertical-align: top;
  }
}

.datePickerRight {
  @media (min-width: breakpoints(tablet)) {
    .ant-picker {
      text-align: right;
    }
  }
}

.description {
  .ant-input {
    min-height: 100px;
    resize: vertical;
  }
}

.divider {
  margin: 24px 0;
  border-color: colors(techGray_20);
}

.noBottomMargin {
  margin-bottom: 0 !important;
}

.locationFormRoot {
  flex: 1;
  flex-direction: column;
  flex-wrap: unset;
}

.locationFormRootFields {
  flex: 1;
}

.maxWidth {
  width: 100%;
}

.checkboxGroup {
  padding-inline: 8px;
}

.checkboxContainer {
  margin-inline: -8px;
}

.formGroupHeader {
  margin-bottom: 12px;
}

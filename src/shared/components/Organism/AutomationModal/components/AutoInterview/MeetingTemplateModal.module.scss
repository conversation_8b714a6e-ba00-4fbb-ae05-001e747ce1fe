// @import 'src/shared/styles/variables';
// @import 'src/shared/styles/mixins';

.largeModalHeader {
  border-bottom: 1px solid colors(techGray_20);
  padding: 20px 24px;
  
  .title {
    font-size: 18px;
    font-weight: 600;
    color: colors(textPrimary);
  }
}

.modalBody {
  padding: 24px;
  height: calc(100vh - 140px);
  overflow-y: auto;
  
  @media (max-width: breakpoints(tablet)) {
    height: calc(100vh - 120px);
    padding: 16px;
  }
}

.footer {
  padding: 16px 24px;
  border-top: 1px solid colors(techGray_20);
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  
  @media (max-width: breakpoints(tablet)) {
    padding: 16px;
  }
}

.divider {
  width: 1px;
  background-color: colors(techGray_20);
  margin: 0 8px;
}

.showBack {
  display: block;
}

.hideBack {
  display: none;
}

.confirmationModalContent {
  max-width: 400px;
  width: 90vw;
}

.wideConfirmationModalContent {
  max-width: 500px;
  width: 90vw;
}

// Animation classes for form motion
#modalMotion {
  width: 100%;
}

// Form specific styles
.formSection {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  color: colors(textPrimary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid colors(techGray_10);
}

// Responsive adjustments
@media (max-width: breakpoints(tablet)) {
  .largeModalHeader {
    padding: 16px;
  }
  
  .footer {
    flex-direction: column;
    gap: 8px;
    
    .divider {
      display: none;
    }
  }
}

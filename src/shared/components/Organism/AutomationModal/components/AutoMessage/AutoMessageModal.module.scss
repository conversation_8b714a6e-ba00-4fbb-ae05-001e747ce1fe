.modal {
  width: 100%;
  max-width: 800px;
  height: 100vh;
  max-height: 100vh;
  margin: 0;
  border-radius: 0;
  background: var(--background-primary);
}

.modalBody {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

.content {
  height: 100%;
  flex-direction: column;
}

.container {
  flex: 1;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.templatesList {
  flex: 1;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  padding-right: 8px;
}

.templateItem {
  width: 100%;
  padding: 16px;
  border-radius: 8px;
  background: var(--background-secondary);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  justify-content: space-between;
  align-items: center;
  gap: 16px;

  &:hover {
    background: var(--background-tertiary);
    border-color: var(--border-secondary);
  }
}

.action {
  flex-shrink: 0;
}

.actionContainer {
  flex-shrink: 0;
}

.defaultButton {
  min-width: 120px;
  height: 32px;
  font-size: 12px;
}

.subTitleComponent {
  flex-direction: row;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.buttonsContainer {
  gap: 12px;
  width: 100%;
}

.discardButton {
  flex: 1;
}

.updateButton {
  flex: 1;
}

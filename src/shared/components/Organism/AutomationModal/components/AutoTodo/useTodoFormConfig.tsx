import { useMemo } from 'react';
import AvatarCard from '@shared/uikit/AvatarCard';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import { searchEndPoints } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';

export const useTodoFormConfig = () => {
  const { t } = useTranslation();

  return useMemo(
    () => [
      {
        name: 'title',
        cp: 'input',
        label: t('Title'),
        maxLength: 30,
        required: true,
        wrapStyle: '!mb-4',
      },
      {
        name: 'assigneeUserId',
        cp: 'avatarAsyncAutoComplete',
        label: t('Assignee'),
        required: true,
        wrapStyle: '!mb-4',
        url: `${searchEndPoints.suggestObject}?includeUsername=true&userType=PERSON`,
        renderItem: ({ item }: any) => (
          <AvatarCard
            containerProps={{
              className: 'itemWrapper',
            }}
            data={{
              title: item.label,
              image: item.image,
              subTitle: item.username,
            }}
            action={
              item?.isLock && (
                <Tooltip
                  placement="left"
                  trigger={<Icon name="lock" type="fal" size={18} />}
                >
                  {t('private')}
                </Tooltip>
              )
            }
          />
        ),
        normalizer: (data: any) =>
          data?.content?.reduce((prev: Array<any>, cur: any) => {
            if (cur.hideIt) {
              return prev;
            }
            return [
              ...prev,
              {
                label: `${cur.name} ${cur.surname}`,
                value: cur.id,
                image: cur.croppedImageUrl,
                job: cur.occupationName,
                username: `@${cur.username}`,
                isPrivate: !cur.allowPageRoleAssign,
                id: cur.id,
              },
            ];
          }, []),
      },
      {
        name: 'description',
        cp: 'richtext',
        label: t('Description'),
        required: true,
        className: '!min-h-[120px]',
        wrapStyle: '!mb-4',
      },
      {
        name: 'attachments',
        cp: 'attachmentPicker',
        label: t('Attachments'),
        type: 'imgAndPdf',
        wrapStyle: '!mb-4',
      },
    ],
    [t]
  );
};

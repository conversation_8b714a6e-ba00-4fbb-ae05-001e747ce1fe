@import '/src/shared/theme/theme.scss';

@layer uikit2 {
  .modalBody {
    padding: 0;
    flex: 1;
    overflow-y: auto;
  }

  .content {
    flex-direction: column;
    gap: variables(largeGutter);
    padding: variables(largeGutter);
  }

  .userInfoBar {
    flex-direction: row;
    align-items: center;
    gap: variables(gutter);
    padding: variables(gutter);
    background-color: colors(gray_5);
    border-radius: 8px;
    border: 1px solid colors(techGray_10);
  }

  .userAvatar {
    flex-shrink: 0;
  }

  .userDetails {
    flex-direction: column;
    gap: 4px;
  }

  .form {
    flex-direction: column;
    gap: variables(largeGutter);
  }

  .assigneeInput {
    width: 100%;
  }

  .titleInput {
    width: 100%;
  }

  .descriptionInput {
    width: 100%;
  }

  .attachmentPicker {
    width: 100%;
  }

  .todoBox {
    flex-direction: column;
    gap: variables(largeGutter);
    padding: variables(largeGutter);
    background-color: colors(gray_5);
    border-radius: 8px;
    border: 1px solid colors(techGray_10);
  }

  .todoTitle {
    color: colors(smoke_coal);
  }

  .assigneeList {
    flex-direction: column;
    gap: variables(gutter);
  }

  .assigneeItem {
    flex-direction: row;
    align-items: center;
    gap: variables(gutter);
    padding: variables(gutter);
    background-color: colors(background);
    border-radius: 6px;
    border: 1px solid colors(techGray_10);
  }

  .assigneeAvatar {
    flex-shrink: 0;
  }

  .assigneeInfo {
    flex-direction: column;
    gap: 2px;
  }

  .modalFooter {
    border-top: 1px solid colors(techGray_10);
    background-color: colors(background);
    padding: variables(largeGutter);
    flex-shrink: 0;
  }

  .footerButtons {
    flex-direction: row;
    gap: variables(gutter);
    justify-content: space-between;
  }

  .discardButton {
    flex: 1;
    background-color: colors(gray_10);
    color: colors(smoke_coal);
    border: 1px solid colors(techGray_20);

    &:hover {
      background-color: colors(gray_20);
    }
  }

  .saveButton {
    flex: 1;
    background-color: colors(brand);
    color: colors(white);

    &:hover {
      background-color: colors(brand_hover);
    }
  }

  .deleteButton {
    flex: 1;
    background-color: colors(error_10);
    color: colors(error);
    border: 1px solid colors(error_20);

    &:hover {
      background-color: colors(error_20);
    }
  }

  .editButton {
    flex: 1;
    background-color: colors(brand_10);
    color: colors(brand);
    border: 1px solid colors(brand_20);

    &:hover {
      background-color: colors(brand_20);
    }
  }

  .deleteSheet {
    padding: variables(largeGutter);
  }

  .deleteSheetContent {
    flex-direction: column;
    gap: variables(largeGutter);
    align-items: center;
    text-align: center;
  }

  .deleteSheetTitle {
    color: colors(smoke_coal);
  }

  .deleteSheetSubtitle {
    color: colors(secondaryText);
    line-height: 1.5;
  }

  .deleteSheetButtons {
    flex-direction: row;
    gap: variables(gutter);
    width: 100%;
  }

  .cancelButton {
    flex: 1;
    background-color: colors(gray_10);
    color: colors(smoke_coal);
    border: 1px solid colors(techGray_20);

    &:hover {
      background-color: colors(gray_20);
    }
  }

  .confirmDeleteButton {
    flex: 1;
    background-color: colors(error);
    color: colors(white);

    &:hover {
      background-color: colors(error_hover);
    }
  }

  @media (min-width: breakpoints(tablet)) {
    .content {
      padding: variables(xLargeGutter);
    }

    .modalFooter {
      padding: variables(xLargeGutter);
    }

    .deleteSheet {
      padding: variables(xLargeGutter);
    }
  }
}

import React from 'react';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import { UserProfile } from '@shared/components/Organism/AutomationModal/components/UserProfile';
import Divider from '@shared/uikit/Divider';
import Flex from '@shared/uikit/Flex';
import Box from '@shared/uikit/Layout/Box';
import Typography from '@shared/uikit/Typography';
import useTranslation from 'shared/utils/hooks/useTranslation';

interface TodoDisplayProps {
  todo: any;
}

export const TodoDisplay: React.FC<TodoDisplayProps> = ({ todo }) => {
  const { t } = useTranslation();

  return (
    <Flex className="w-full p-8">
      <Box className="bg-gray_5 rounded-lg p-4 shadow-sm border border-solid border-techGray_10">
        {todo?.title && (
          <Flex
            className="font-bold text-lg text-smoke_coal mb-2 [&_*]:text-smoke_coal"
            dangerouslySetInnerHTML={{ __html: todo?.title }}
          />
        )}
        {todo?.text && (
          <Flex
            className="[&_*]:!text-sm [&_*]:!text-smoke_coal text-smoke_coal mb-4"
            dangerouslySetInnerHTML={{ __html: todo?.text }}
          />
        )}
        <Divider className="my-[12px]" />
        <Flex className="gap-10">
          <Flex>
            <Typography className="text-xs font-medium !text-colorIconForth2 mb-8">
              {t('Assignee')}
            </Typography>
            <UserProfile
              croppedImageUrl={todo?.assigneeUser?.croppedImageUrl}
              username={todo?.assigneeUser?.username}
              role={todo?.assigneeUser?.occupationName}
              variant="card"
            />
          </Flex>

          <Flex>
            <Typography className="text-xs font-medium !text-colorIconForth2 mb-8">
              {t('Creator')}
            </Typography>
            <UserProfile
              croppedImageUrl={todo?.user?.croppedImageUrl}
              username={todo?.user?.username}
              role={todo?.user?.occupationName}
              variant="card"
            />
          </Flex>
        </Flex>

        {/* Attachments */}
        {todo?.fileIds && todo.fileIds.length > 0 && (
          <PrivateAttachmentsList
            ids={todo.fileIds.map((id: any) => Number(id))}
          />
        )}
      </Box>
    </Flex>
  );
};

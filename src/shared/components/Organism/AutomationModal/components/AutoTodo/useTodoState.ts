import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { transformAssigneeUser } from '@shared/components/Organism/AutomationModal/utils';
import {
  getPipelineAutoTodo,
  putPipelineAutoTodo,
  deletePipelineAutoTodo,
} from '@shared/utils/api/pipeline';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import { mapFileIdsToAttachments, mapAttachmentsToFileIds } from '../utils';
import type {
  PipelineAutoTodoRequest,
  PipelineAutoTodoResponse,
} from '@shared/utils/api/pipeline';

interface FormValues {
  title: string;
  description: string;
  assigneeUserId:
    | number
    | null
    | {
        label: string;
        value: number;
        image: string;
        job: string;
        username: string;
        isPrivate: boolean;
        id: number;
      };
  allTeamMembersTagged: boolean;
  taggedUserIds: number[];
  attachments: Array<{ id: string }>;
}

interface UseTodoStateProps {
  isOpen: boolean;
  pipelineId: number;
}

export const useTodoState = ({ isOpen, pipelineId }: UseTodoStateProps) => {
  const [editMode, setEditMode] = useState(false);

  const queryClient = useQueryClient();

  const {
    data: todo,
    isLoading,
    refetch,
  } = useReactQuery<PipelineAutoTodoResponse | null>({
    action: {
      key: ['pipelineAutoTodo', pipelineId],
      apiFunc: () => getPipelineAutoTodo(pipelineId),
    },
    config: {
      enabled: isOpen && !!pipelineId,
      refetchOnMount: false,
    },
  });

  const updateMutation = useReactMutation<
    PipelineAutoTodoResponse,
    PipelineAutoTodoRequest
  >({
    apiFunc: (body) => putPipelineAutoTodo(pipelineId, body),
    mutationKey: ['pipelineAutoTodoUpdate', pipelineId],
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['pipelineAutoTodo', pipelineId],
      });
    },
  });

  const deleteMutation = useReactMutation<void>({
    apiFunc: () => deletePipelineAutoTodo(pipelineId),
    mutationKey: ['pipelineAutoTodoDelete', pipelineId],
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['pipelineAutoTodo', pipelineId],
      });
    },
  });

  React.useEffect(() => {
    if (todo) {
      setEditMode(false);
    } else if (isOpen && pipelineId) {
      setEditMode(true);
    }
  }, [todo, isOpen, pipelineId]);

  const getInitialFormValues = (): FormValues => {
    let assigneeUserObj = null;
    if (todo?.assigneeUser) {
      assigneeUserObj = transformAssigneeUser(todo);
    }

    return {
      title: todo?.title || '',
      description: todo?.text || '',
      assigneeUserId: assigneeUserObj,
      allTeamMembersTagged: false,
      taggedUserIds: [],
      attachments: mapFileIdsToAttachments(todo?.fileIds ?? []),
    };
  };

  const handleUpdate = async (values: FormValues) => {
    if (!pipelineId) return;

    let assigneeId = 0;
    if (values.assigneeUserId) {
      if (
        typeof values.assigneeUserId === 'object' &&
        'value' in values.assigneeUserId
      ) {
        assigneeId = (values.assigneeUserId as { value: number }).value;
      } else {
        assigneeId = values.assigneeUserId as number;
      }
    }

    const body: PipelineAutoTodoRequest = {
      title: values.title,
      description: values.description,
      assigneeUserId: assigneeId,
      allTeamMembersTagged: values.allTeamMembersTagged,
      taggedUserIds: values.taggedUserIds,
      fileIds: mapAttachmentsToFileIds(values.attachments || []).map(Number),
    };
    await updateMutation.mutateAsync(body);
    setEditMode(false);
  };

  const handleDelete = async (resetForm: () => void) => {
    if (!pipelineId) return;

    await deleteMutation.mutateAsync(undefined);
    setEditMode(true);
    resetForm();
  };

  const setEditModeTrue = async (resetForm: (values: FormValues) => void) => {
    const result = await refetch();
    const freshTodo = result.data || todo;
    let assigneeUserObj = null;
    if (freshTodo?.assigneeUser) {
      assigneeUserObj = transformAssigneeUser(freshTodo);
    }
    if (freshTodo) {
      resetForm({
        title: freshTodo.title || '',
        description: freshTodo.text || '',
        assigneeUserId: assigneeUserObj,
        allTeamMembersTagged: false,
        taggedUserIds: [],
        attachments: mapFileIdsToAttachments(freshTodo.fileIds ?? []),
      });
    }
    setEditMode(true);
  };

  const isMutating = deleteMutation.isPending || updateMutation.isPending;

  return {
    todo,
    user: todo?.user,
    pipeline: todo?.pipeline,
    editMode,
    setEditMode,
    isMutating,
    isLoading,
    handleUpdate,
    handleDelete,
    setEditModeTrue,
    getInitialFormValues,
    updateMutation,
    deleteMutation,
  };
};

import React from 'react';
import { AutomationModalWrapper } from '@shared/components/Organism/AutomationModal/components/AutomationModalWrapper';
import { useTodoState } from '@shared/components/Organism/AutomationModal/components/AutoTodo/useTodoState';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { TodoDisplay } from './TodoDisplay';
import { TodoForm } from './TodoForm';

const AutoTodoModal: React.FC = () => {
  const automationState = useMultiStepFormState('automation');
  const pipelineId = Number((automationState?.data as any)?.id);
  const isOpen = automationState?.isOpen || false;

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const {
    todo,
    editMode,
    isLoading,
    setEditMode,
    isMutating,
    handleUpdate,
    handleDelete,
    setEditModeTrue,
    getInitialFormValues,
  } = useTodoState({ isOpen, pipelineId });

  const { t } = useTranslation();

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const renderFooter = (formikProps: any) => {
    const { values, isValid, resetForm, setValues, isSubmitting } = formikProps;
    if (editMode || !todo) {
      return (
        <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0 w-full">
          <Button
            label={t('discard')}
            schema="gray"
            variant="default"
            onClick={() => {
              if (todo) {
                setEditMode(false);
                setValues(getInitialFormValues());
              } else {
                resetForm();
              }
            }}
            className="flex-1"
          />
          <Button
            label={t('save')}
            schema="primary-blue"
            variant="default"
            onClick={() => handleUpdate(values)}
            className="flex-1"
            disabled={isMutating || isSubmitting || !isValid}
            isLoading={isMutating || isSubmitting}
          />
        </Flex>
      );
    }

    return (
      <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0 w-full">
        <Button
          label={t('delete')}
          schema="transparent"
          variant="default"
          onClick={() => {
            openConfirmDialog({
              title: t('delete_todo'),
              message: t('delete_todo_confirm'),
              confirmButtonText: t('delete'),
              cancelButtonText: t('cancel'),
              isAjaxCall: true,
              apiProps: {
                func: () => handleDelete(resetForm),
                onSuccess: () => setEditModeTrue(setValues),
              },
            });
          }}
          className="flex-1"
          leftIcon="trash"
          disabled={isMutating || isSubmitting}
          isLoading={isMutating || isSubmitting}
        />
        <Button
          label={t('edit')}
          schema="semi-transparent"
          leftIcon="edit"
          variant="default"
          onClick={() => setEditModeTrue(setValues)}
          className="flex-1"
        />
      </Flex>
    );
  };

  const getModalTitle = () => {
    if (!todo) return t('create_todo');

    return editMode ? t('edit_todo') : t('todo_detail');
  };

  if (isLoading) {
    return null;
  }

  return (
    <Form
      initialValues={getInitialFormValues ? getInitialFormValues() : {}}
      onSuccess={handleUpdate}
      enableReinitialize
      local
    >
      {(formikProps) => (
        <AutomationModalWrapper
          isOpen={isOpen}
          onClose={handleClose}
          title={getModalTitle()}
          footer={renderFooter(formikProps)}
          backButtonProps={{ onClick: handleClose }}
          noCloseButton
          hideBack={false}
        >
          {editMode || !todo ? <TodoForm /> : <TodoDisplay todo={todo} />}
        </AutomationModalWrapper>
      )}
    </Form>
  );
};

export default AutoTodoModal;

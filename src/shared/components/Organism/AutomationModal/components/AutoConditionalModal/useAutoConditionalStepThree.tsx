import React from 'react';
import Templates from '@shared/components/Organism/AutomationModal/components/TemplateForm/Templates';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { TemplateFormData } from '@shared/components/Organism/AutomationModal/types/template.types';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter?: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

interface UseAutoConditionalStepThreeProps {
  formData: any;
  onTemplateSelected: (templateId: TemplateFormData) => void;
}

export function useAutoConditionalStepThree({
  formData,
  onTemplateSelected,
}: UseAutoConditionalStepThreeProps): SingleDataItem[] {
  const { t } = useTranslation();

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('select_template'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
  });

  const renderBody: SingleDataItem['renderBody'] = ({ setStep }) => (
    <div className="h-full">
      <Templates
        onClose={() => setStep(1)}
        onUse={(result) => {
          onTemplateSelected(result);
          setStep(1);
        }}
      />
    </div>
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '3',
      getHeaderProps,
      renderBody,
    },
  ];

  return data;
}

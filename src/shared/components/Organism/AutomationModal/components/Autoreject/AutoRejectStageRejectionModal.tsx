import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import EnhancedTemplateForm from '@shared/components/Organism/AutomationModal/components/TemplateForm/EnhancedTemplateForm';
import Templates from '@shared/components/Organism/AutomationModal/components/TemplateForm/Templates';
import { useTemplateActions } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateActions';
import { useTemplateForm } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateForm';
import { useTemplateList } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateList';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useResponseToast from '@shared/hooks/useResponseToast';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import Switch from '@shared/uikit/Switch';
import Typography from '@shared/uikit/Typography';
import { updateRejectTemplate } from '@shared/utils/api/template';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  NormalizedTemplate,
  TemplateFormData,
} from '@shared/components/Organism/AutomationModal/types/template.types';

const AutoRejectStageRejectionModal = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const [showForm, setShowForm] = useState(false);
  const { handleSuccess } = useResponseToast();
  const params = useParams();
  const pipelineId = Number((automationState?.data as any)?.id);
  const [showAutoReply, setShowAutoReply] = useState(false);
  const [templateFormValue, setTemplateFormValue] = useState<
    TemplateFormData | undefined
  >(undefined);

  const onUpdateRejectTemplate = (result: any) => {
    putPipelineAutoRejectMutation({
      body: {
        fileIds: (result.fileIds || []).map((id: any) => id.toString()),
        hasFollowup: result?.hasFollowup,
        message: result?.message,
        subject: result?.subject,
        timeDelay: result?.timeDelay,
        title: result?.title,
        followupFileIds: result?.followupFileIds,
        followupMessage: result?.followupMessage,
        followupPeriod: result?.followupPeriod,
        followupTitle: result?.followupTitle,
      },
      id: pipelineId,
    });
  };

  const {
    mutate: putPipelineAutoRejectMutation,
    isPending: isUpdatingPending,
  } = useReactMutation({
    apiFunc: updateRejectTemplate,
    onSuccess: (result) => {
      handleSuccess({
        message: t('auto_reject_created_message'),
        title: t('auto_reject_created_title'),
      });
    },
  });

  const templateForm = useTemplateForm({
    onFormChange: (_values, _isValid) => {},
    onSubmit: (data, isCreate) => {
      templateActions.createTemplate(data, (result) => {
        onUpdateRejectTemplate(result);
      });
    },
  });

  const templateList = useTemplateList({
    searchEnabled: true,
    onSuccess: (template) => {
      if (template?.length) {
        templateForm.startEditing(
          template?.find(
            (item) => item?.id === templateForm?.editingTemplate?.id
          ) as unknown as NormalizedTemplate
        );
      }
    },
  });

  const templateActions = useTemplateActions(
    {
      onTemplateCreated: () => {
        templateList.refetch();
        // may we need to check update rejction
      },
    },
    {
      message: t('auto_reply_duplicated_message'),
      title: t('auto_reply_duplicated_title'),
    }
  );

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'autoReject',
    });
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    templateForm.resetForm();
  };

  if (showAutoReply) {
    return (
      <Templates
        onClose={() => setShowAutoReply(false)}
        onUse={(result) => {
          setTemplateFormValue(result);
          setShowAutoReply(false);
        }}
      />
    );
  }

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={t('stage_rejection')}
        backButtonProps={{
          onClick: showForm ? handleFormDiscard : handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody className="p-6 h-full overflow-auto mb-8">
        <Flex className="gap-20">
          <Flex className="gap-12">
            <Typography className="text-colorIconForth2 text-xs font-bold">
              {t('reject_candidate_if')}
            </Typography>

            <Flex
              flexDir="row"
              className="!justify-between rounded items-center p-12 border border-solid border-techGray_20 bg-gray_5"
            >
              <Typography className="!text-smoke_coal text-sm">
                {t('move_to')} {automationState?.data?.title}
              </Typography>

              <div>
                <Switch
                  value={showForm}
                  onChange={() => {
                    setShowForm((prev) => !prev);
                  }}
                />
              </div>
            </Flex>
          </Flex>
          {showForm && (
            <EnhancedTemplateForm
              formData={templateFormValue || templateForm.formData}
              onSubmit={templateForm.handleSubmit}
              isLoading={templateForm.isLoading}
              onChange={templateForm.handleFormChange}
              hasDefault={false}
              config={{
                showDelay: true,
                showFollowup: true,
                showAttachments: true,
              }}
              title={t('auto_reply')}
              onShowTemplates={() => setShowAutoReply(true)}
            />
          )}
        </Flex>
      </ModalBody>

      <ModalFooter>
        <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
          <Button
            label={t('discard')}
            schema="gray"
            variant="default"
            onClick={handleFormDiscard}
            className="flex-1"
          />
          <Button
            label={t('save')}
            schema="primary-blue"
            variant="default"
            onClick={() => templateForm.handleSubmit()}
            className="flex-1"
            disabled={!templateForm.canSubmit}
            isLoading={templateForm.isLoading || templateActions.isMutating}
          />
        </Flex>
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default AutoRejectStageRejectionModal;

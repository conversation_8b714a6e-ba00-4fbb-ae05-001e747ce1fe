import React from 'react';
import Avatar from '@shared/uikit/Avatar';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';

interface UserProfileProps {
  croppedImageUrl?: string;
  username?: string;
  role?: string;
  variant?: 'header' | 'card';
  className?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  croppedImageUrl,
  username,
  role,
  variant = 'header',
  className = '',
}) => {
  const { t } = useTranslation();
  const displayName = username ? `${username} (${t('you')})` : t('you');

  if (variant === 'header') {
    return (
      <Flex
        flexDir="row"
        className={`items-center h-[62px] p-6 bg-gray_5 gap-8 mb-4 ${className}`}
      >
        <Avatar imgSrc={croppedImageUrl} size="md" />
        <Flex flexDir="column">
          <Typography className="font-bold !text-white">
            {displayName}
          </Typography>
          <Typography className="text-xs text-gray-500">{role}</Typography>
        </Flex>
      </Flex>
    );
  }

  return (
    <Flex flexDir="row" className={`gap-8 mb-8 ${className}`}>
      <Avatar imgSrc={croppedImageUrl} size="md" />
      <Flex flexDir="column">
        <Typography className="font-bold text-base !text-white">{displayName}</Typography>
        <Typography className="text-sm !text-secondaryDisabledText">{role}</Typography>
      </Flex>
    </Flex>
  );
};

import React from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import MenuItem from '@shared/uikit/MenuItem';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { IconName } from '@shared/uikit/Icon/types';
import { AutomationStageType } from '@shared/components/Organism/AutomationModal/components/AutomationStageType';

const AutoMoveModal: React.FC = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const handleRequirementsClick = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'move_requirements',
    });
  };

  const autoMoveItems = [
    {
      icon: 'assessment' as IconName,
      title: t('auto_assessment'),
      subTitle: t('m_c_t_o_o_b_s_q_s_a_s'),
    },
    {
      icon: 'form' as IconName,
      title: t('requirements'),
      subTitle: t('m_c_t_o_o_b_s_q_s_a'),
      onClick: handleRequirementsClick,
    },
  ];

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('auto_move')} helper={t('m_a_b_o_c_d')} />
      <ModalBody className="p-0 md:p-6 bg-darkSecondary overflow-y-auto">
        <Flex className="p-4 flex-col gap-6">
          <Flex className="flex-row items-center gap-2 mb-6">
            <AutomationStageType type="autoMove" />
          </Flex>
          <Flex className="flex-col gap-4 mt-6">
            {autoMoveItems.map((item, index) => (
              <MenuItem
                key={index}
                iconName={item.icon}
                iconType="far"
                iconSize={24}
                title={item.title}
                subTitle={item.subTitle}
                onClick={item.onClick}
                withHover
                actionElement={
                  <Icon
                    name="chevron-right"
                    type="fas"
                    size={20}
                    color="white"
                  />
                }
              />
            ))}
          </Flex>
        </Flex>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default AutoMoveModal;

import React from 'react';
import { UserProfile } from '@shared/components/Organism/AutomationModal/components/UserProfile';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Box from '@shared/uikit/Layout/Box';
import { useNoteFormConfig } from './NoteFormConfig';

export const NoteForm = () => {
  const formGroups = useNoteFormConfig();
  const { authUser } = useGetAppObject();

  return (
    <>
      <UserProfile
        croppedImageUrl={authUser?.croppedImageUrl}
        username={authUser?.username}
        variant="header"
        role={authUser?.occupation?.label}
      />
      <Box className="bg-gray-50 rounded-lg p-4 shadow-sm border border-gray-200">
        <DynamicFormBuilder groups={formGroups} />
      </Box>
    </>
  );
};

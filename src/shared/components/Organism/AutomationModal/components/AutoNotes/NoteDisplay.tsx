import React from 'react';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import { UserProfile } from '@shared/components/Organism/AutomationModal/components/UserProfile';
import { formatTime } from '@shared/components/Organism/AutomationModal/components/utils';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Box from '@shared/uikit/Layout/Box';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { PipelineAutoNote } from '@shared/utils/api/pipeline';

interface NoteDisplayProps {
  note: PipelineAutoNote | null;
}

export const NoteDisplay: React.FC<NoteDisplayProps> = ({ note }) => {
  const { authUser } = useGetAppObject();
  const { t } = useTranslation();

  return (
    <Flex className="w-full p-8">
      <Box className="bg-gray_5 rounded-lg p-4 shadow-sm border border-solid border-techGray_10">
        <UserProfile
          croppedImageUrl={authUser?.croppedImageUrl}
          username={authUser?.username}
          role={authUser?.occupation?.label}
          variant="card"
        />
        {note?.text && (
          <Flex
            className="*:text-xs text-white mb-4"
            dangerouslySetInnerHTML={{ __html: note?.text }}
          />
        )}

        {note?.fileIds && note?.fileIds?.length > 0 && (
          <PrivateAttachmentsList
            ids={note?.fileIds?.map((id) => Number(id))}
          />
        )}
        <Flex flexDir="row" className="justify-between items-center mt-4">
          <Flex flexDir="row" className="items-center gap-2">
            <IconButton
              name="users"
              type="far"
              size="sm18"
              colorSchema="transparent"
            />
            <Typography className="text-xs text-gray-400">
              {note?.visibility === 'EVERYONE' ? t('team') : t('only_me')}
            </Typography>
          </Flex>
          <Typography className="text-xs text-gray-400">
            {formatTime(note?.updatedAt || note?.createdAt)}
          </Typography>
        </Flex>
      </Box>
    </Flex>
  );
};

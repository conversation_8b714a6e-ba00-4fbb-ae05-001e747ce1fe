import React from 'react';
import IconButton from '@shared/uikit/Button/IconButton';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import useTranslation from '@shared/utils/hooks/useTranslation';

export interface NoteFormConfig {
  name: string;
  cp: string;
  label: string;
  required?: boolean;
  variant?: string;
  className?: string;
  labelProps?: Record<string, any>;
  type?: string;
  formGroup?: Record<string, any>;
  options?: Array<{ label: string; value: string; hint?: { content: string } }>;
  rightIcon?: React.ReactNode;
}

export const useNoteFormConfig = (): NoteFormConfig[] => {
  const { t } = useTranslation();
  return [
    {
      name: 'visibility',
      cp: 'radioGroup',
      label: '',
      classNames: {
        root: '!flex !flex-column',
        itemWrapper: '!mb-0',
        container: '!gap-0',
      },
      formGroup: {
        title: t('visibility'),
        color: 'colorIconForth2',
      },
      options: [
        {
          label: t('team'),
          value: 'EVERYONE',
          rightComponent: (
            <Tooltip
              placement="top"
              trigger={
                <Icon
                  color="smoke_coal"
                  type="fal"
                  name="info-circle"
                  size={15}
                />
              }
            >
              {t('visible_to_your_team')}
            </Tooltip>
          ),
        },
        {
          label: t('only_me'),
          value: 'ONLY_ME',
          rightComponent: (
            <Tooltip
              placement="top"
              trigger={
                <Icon
                  color="smoke_coal"
                  type="fal"
                  name="info-circle"
                  size={15}
                />
              }
            >
              {t('visible_to_you_only')}
            </Tooltip>
          ),
        },
      ],
      required: true,
      rightIcon: (
        <IconButton
          name="info-circle"
          type="far"
          size="sm18"
          colorSchema="transparent"
        />
      ),
      className: 'mb-4',
    },
    {
      name: 'text',
      cp: 'richtext',
      label: t('write_note'),
      required: true,
      className: 'mb-4 !min-h-[225px]',
      labelProps: { color: 'secondaryDisabledText' },
    },
    {
      name: 'attachments',
      cp: 'attachmentPicker',
      label: t('attachments'),
      type: 'imgAndPdf',
      className: 'mb-4',
    },
  ];
};

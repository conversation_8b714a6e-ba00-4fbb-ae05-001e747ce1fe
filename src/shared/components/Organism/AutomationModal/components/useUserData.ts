import useGetAppObject from '@shared/hooks/useGetAppObject';

interface UserData {
  croppedImageUrl: string;
  username: string;
}

export const useUserData = (): UserData => {
  const { getAppObjectPropValue } = useGetAppObject();

  const croppedImageUrl = getAppObjectPropValue({
    userKey: 'croppedImageUrl',
    pageKey: 'croppedImageUrl',
  });

  const username = getAppObjectPropValue({
    pageKey: 'username',
    userKey: 'username',
  });

  return {
    croppedImageUrl,
    username,
  };
};

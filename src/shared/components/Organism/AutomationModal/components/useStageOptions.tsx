import React from 'react';
import IconButton from '@shared/uikit/Button/IconButton';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { StageOption } from './types';

export const useStageOptions = (): StageOption[] => {
  const { t } = useTranslation();

  return [
    {
      value: 'review',
      label: t('Review'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          variant="rectangle"
          iconProps={{ color: 'trench' }}
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'interview',
      label: t('Interview'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'error' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'hiring',
      label: t('Hiring'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'pendingOrange' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'offered',
      label: t('Offered'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'success' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
  ];
};

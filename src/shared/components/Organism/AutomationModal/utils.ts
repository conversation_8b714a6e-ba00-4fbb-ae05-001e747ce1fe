import type {
  PipelineAutoNote,
  PipelineAutoNoteResponse,
  PipelineAutoTodoResponse,
} from '@shared/utils/api/pipeline';

export function transformNote(
  note?: PipelineAutoNoteResponse
): PipelineAutoNote | null {
  if (!note) return null;

  return {
    id: note.pipeline?.id,
    pipelineId: note.pipeline?.id,
    visibility: note.visibility,
    text: note.text,
    fileIds: note.fileIds ?? [],
    createdAt: note.user?.birthDate,
    updatedAt: note.user?.birthDate,
    user: {
      id: note.user?.id,
      name: note.user?.name,
      role: note.user?.occupationName,
      avatarUrl: note.user?.croppedImageUrl,
    },
  };
}

export function transformAssigneeUser(freshTodo?: PipelineAutoTodoResponse) {
  if (!freshTodo?.assigneeUser) return null;

  const user = freshTodo.assigneeUser;

  return {
    label: `${user.name} ${user.surname}`,
    value: user.id,
    image: user.croppedImageUrl,
    job: user.occupationName,
    username: user.username ? `@${user.username}` : '',
    isPrivate: !user.allowPageRoleAssign,
    id: user.id,
  };
}

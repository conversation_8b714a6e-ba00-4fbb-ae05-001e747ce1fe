import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useTemplateBulkAction } from '@shared/components/Organism/BulkActions/hooks/useTemplateBulkAction';
import { CANDIDATE_NOTE_MAX_LENGTH } from '@shared/constants/enums';
import AsyncAutoComplete from '@shared/uikit/AutoComplete/AsyncAutoComplete';
import { jobsEndpoints } from '@shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export type PipelineItem = {
  value?: number;
  label?: string;
};

type PipelineBulkActionsStepTwo = {
  pipelineItem?: PipelineItem;
  setPipelineItem: React.Dispatch<React.SetStateAction<PipelineItem>>;
};

export function usePipelineBulkActionsStepTwo({
  pipelineItem,
  setPipelineItem,
}: PipelineBulkActionsStepTwo): SingleDataItem[] {
  const { t } = useTranslation();
  const params = useParams();
  const jobId = params?.id as string;

  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    message: string;
    fieIds: number[];
  } | null>(null);

  const moveToStep = useTemplateBulkAction({
    cancelLabel: t('discard'),
    headerLabel: t('move_to'),
    item: formData?.item,
    onChange: (item) => {
      setFormData({
        item: { value: item.id, label: item.title },
        subject: item?.subject,
        message: item?.message,
        fieIds: item?.fileIds,
      });
    },
    introElement: (
      <AsyncAutoComplete
        variant="simple-large"
        label={t('move_to')}
        name="pipelines"
        placeholder={t('move_to')}
        rightIconProps={{ name: 'chevron-down', size: 'md18' }}
        visibleRightIcon
        onChange={(val: any) => setPipelineItem(val)}
        url={jobsEndpoints.getPipeline(jobId)}
        normalizer={(res: any) =>
          res?.pipelines?.map((item: any) => ({
            value: item?.id,
            label: item?.title,
          }))
        }
      />
    ),
    disabledSubmit: !pipelineItem?.value && !formData?.item?.value,
    stepKey: '2',
    submitLabel: t('move'),
    backStep: 0,
    additionalFields: [
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: false,
        disabled: true,
        readOnly: true,
        value: formData?.subject,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t(`message`),
        showEmoji: false,
        className: '!h-[252px] overflow-auto',
        helperText: t('dynamic_template_helper_text'),
        disabled: true,
        required: false,
        readonly: true,
        visibleOptionalLabel: false,
        value: formData?.message,
        defaultValue: formData?.message,
        maxLength: CANDIDATE_NOTE_MAX_LENGTH,
        disabledReadOnly: true,
      },
      {
        name: 'attachmentFileIds',
        cp: 'attachmentPicker',
        value: formData?.fieIds,
        wrapStyle: 'pointer-events-none',
        label: t('attachment'),
        required: false,
        visibleOptionalLabel: false,
        disabledReadOnly: true,
      },
    ],
  });

  return moveToStep;
}

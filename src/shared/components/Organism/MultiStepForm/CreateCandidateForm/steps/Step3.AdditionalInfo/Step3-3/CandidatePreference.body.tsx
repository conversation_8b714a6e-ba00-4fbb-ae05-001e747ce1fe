import { useMemo } from 'react';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import { Endpoints } from '@shared/utils/constants';
import {
  NOTICE_PERIOD_VALUES,
  RELOCATION_STATUS_VALUES,
} from '@shared/utils/constants/enums';
import { EMPLOYMENT_TYPES } from '@shared/utils/constants/enums/db';
import {
  experienceLevels,
  SALARY_PERIOD,
  WILLING_TRAVEL,
  WORK_SPACE_MODEL,
} from '@shared/utils/constants/enums/jobsDb';
import lookupResponseNormalizer from '@shared/utils/normalizers/lookupResponseNormalizer';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import geoApi from 'shared/utils/api/geo';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from '../../../CreateCandidate.module.scss';
import type { CallbackParams } from '@shared/components/Organism/MultiStepForm/MultiStepForm';

const CandidatePreferenceBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();

  const formGroups = useMemo(
    () => [
      {
        formGroup: {
          title: t('job_preferences'),
          formSection: true,
        },
        wrapStyle: classes.formItemWrapStyle,
        name: 'preferredJob',
        cp: 'asyncAutoComplete',
        maxLength: 100,
        visibleRightIcon: true,
        rightIconProps: { name: 'search' },
        label: t('job_title'),
        url: Endpoints.App.Common.getOccupations,
        normalizer: lookupResponseNormalizer,
      },
      /** LINE 2 */
      {
        name: 'preferredWorkPlaceType',
        cp: 'dropdownSelect',
        label: t('workspace'),
        isFirstHalfWidth: true,
        options: WORK_SPACE_MODEL,
      },
      {
        name: 'preferredEmploymentType',
        cp: 'dropdownSelect',
        label: t('job_type'),
        isSecondHalfWidth: true,
        options: EMPLOYMENT_TYPES,
      },
      /** LINE 3 */
      {
        name: 'preferredExperienceLevel',
        cp: 'dropdownSelect',
        label: t('exp_level'),
        isFirstHalfWidth: true,
        options: experienceLevels,
      },
      {
        name: 'noticePeriod',
        cp: 'dropdownSelect',
        label: t('notice_period'),
        isSecondHalfWidth: true,
        options: Object.values(NOTICE_PERIOD_VALUES),
      },
      /** LINE 4 */
      {
        name: 'preferredLocation',
        apiFunc: geoApi.suggestPlace,
        cp: 'asyncAutoCompleteWithExtraParams',
        label: t('preferred_location'),
        isFirstHalfWidth: true,
        visibleRightIcon: true,
        rightIconProps: { name: 'search' },
      },
      {
        name: 'relocation',
        cp: 'dropdownSelect',
        label: t('relocation'),
        isSecondHalfWidth: true,
        options: Object.values(RELOCATION_STATUS_VALUES),
      },
      {
        name: 'travelRequirement',
        cp: 'dropdownSelect',
        label: t('willing_to_travel'),
        wrapStyle: classes.halfWidthWrapperStyle,
        options: WILLING_TRAVEL,
      },
      {
        name: 'form_group_1',
        formGroup: {
          title: t('expected_pay'),
          formSection: true,
          className: classes.formGroupLabel,
        },
        wrapStyle: classes.formItemWrapStyle,
        cp: () => null,
      },

      {
        name: 'expectedCurrency',
        label: t('currency'),
        cp: 'asyncAutoComplete',
        type: 'text',
        visibleRightIcon: true,
        isFirstHalfWidth: true,
        rightIconProps: { name: 'search' },
        url: Endpoints.App.Common.searchCurrency,
        normalizer: (data: any) =>
          data?.map(({ id: value, name, symbol, code }: any) => ({
            label: `${name} (${symbol})`,
            name,
            symbol,
            value,
            code,
          })),
      },
      {
        name: 'expectedSalaryPeriod',
        cp: 'dropdownSelect',
        label: t('salary_period'),
        isSecondHalfWidth: true,
        options: SALARY_PERIOD,
      },
      /** LINE 6 */
      {
        name: 'expectedMinimumSalary',
        label: t('min_salary'),
        type: 'number',
        forceVisibleError: true,
        isFirstHalfWidth: true,
        cp: 'input',
      },
      {
        name: 'expectedMaximumSalary',
        label: t('max_salary'),
        isSecondHalfWidth: true,
        type: 'number',
        forceVisibleError: true,
        cp: 'input',
      },
    ],
    [t]
  );

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder className={classes.formBuilder} groups={formGroups} />
    </ModalTransitionWrapper>
  );
};

export default CandidatePreferenceBody;

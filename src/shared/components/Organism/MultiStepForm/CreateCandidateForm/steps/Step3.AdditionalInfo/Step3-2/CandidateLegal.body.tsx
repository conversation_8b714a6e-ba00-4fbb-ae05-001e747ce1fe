import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { PrivateAttachmentsList } from '@shared/components/molecules/Attachments';
import Info from '@shared/components/molecules/Info/Info';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import { pickTypes } from '@shared/uikit/FilePicker';
import Flex from '@shared/uikit/Flex';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import useMedia from '@shared/uikit/utils/useMedia';
import { searchPages } from '@shared/utils/api/search';
import storageApis from '@shared/utils/api/storage';
import { ID_DOC_TYPE_VALUES } from '@shared/utils/constants/enums';
import {
  geoEndpoints,
  lookupEndpoints,
  storageEndPoints,
} from '@shared/utils/constants/servicesEndpoints';
import lookupResponseNormalizer from '@shared/utils/normalizers/lookupResponseNormalizer';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import fileApi from 'shared/utils/api/file';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import lookupNormalizer from '@shared/utils/normalizers/lookup';
import hereApiResponseNormalizer from '@shared/utils/normalizers/hereApiResponseNormalizer';
import geoNormalizer from '@shared/utils/normalizers/geo';
import Typography from '@shared/uikit/Typography';
import classes from '../../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import type { CallbackParams } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type { CandidateFormData } from '@shared/types/candidates';
import type { AttachmentPickerProps } from '@shared/uikit/AttachmentPicker/AttachmentPicker.component';

const CandidateLegalBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { authUser } = useGetAppObject();
  const { isLoboxUser, candidate } = useCandidateModalContext();
  const { values, setFieldValue } = useFormikContext<CandidateFormData>();

  const formGroups = useMemo(() => {
    const date = new Date();
    const maxDate = new Date(date.setFullYear(date.getFullYear() + 20));
    const minDate = new Date();
    const countryId = values?.country?.value;

    return {
      country: [
        {
          formGroup: {
            title: t('residency_location'),
            formSection: true,
          },
          wrapStyle: classes.formItemWrapStyle,
          name: 'country',
          label: t('country'),
          cp: 'asyncAutoCompleteWithExtraParams',
          url: geoEndpoints.location.searchLocation,
          normalizer: geoNormalizer.updatedSearchCountry,
          forceVisibleError: true,
          visibleRightIcon: true,
          rightIconProps: { name: 'search' },
        },
      ],
      ssn: [
        {
          name: 'identificationDocument',
          formGroup: {
            title: t('id_details'),
            formSection: true,
            className: '!mt-4',
            color: countryId ? 'primaryText' : 'colorIconForth2',
          },
          cp: 'dropdownSelect',
          // disabledReadOnly: isLoboxUser,
          wrapStyle: classes.formItemWrapStyle,
          label: t('id_type'),
          options: Object.values(ID_DOC_TYPE_VALUES),
          disabled: !countryId,
        },
        {
          name: 'ssn',
          // disabledReadOnly: isLoboxUser,
          label: values.identificationDocument?.label
            ? t(values.identificationDocument?.label)
            : t('social_security_number'),
          disabled: !countryId || !values.identificationDocument?.value,
          helperText: t('ssn_helper_text'),
          // type: 'number',
          disabledReadOnly: isLoboxUser || candidate?.ssnEnteredByUser,
          wrapStyle: classes.formItemWrapStyle,
          cp: 'input',
        },
      ],
      visa: [
        {
          formGroup: {
            title: t('legal_status'),
            formSection: true,
            className: '!mt-4',
            color: countryId ? 'primaryText' : 'colorIconForth2',
          },
          wrapStyle: classes.formItemWrapStyle,
          name: 'workAuthorization',
          label: t('work_authorization'),
          cp: 'asyncAutoComplete',
          url: Endpoints.App.Common.searchAuthorization,
          params: {
            countryCode: countryId?.toLowerCase(),
          },
          showDropDownWithoutEnteringAnything: true,
          normalizer: lookupResponseNormalizer,
          forceVisibleError: true,
          visibleRightIcon: true,
          rightIconProps: { name: 'search' },
          disabled: !countryId,
        },
        {
          name: 'workAuthorizationExpiryDate',
          label: t('work_authorization_expiry'),
          cp: 'datePicker',
          variant: 'input',
          wrapStyle: classes.formItemWrapStyle,
          forceVisibleError: true,
          disabled: !countryId,
          maxDate,
          minDate,
        },
        {
          name: 'visaHeldByUser',
          label: t('visa_held_by'),
          cp: 'avatarAsyncAutoComplete',
          apiFunc: searchPages,
          forceVisibleError: true,
          wrapStyle: classes.formItemWrapStyle,
          normalizer: (data: { content: any[] }) =>
            hereApiResponseNormalizer(data?.content ?? [], -1)?.filter(
              (item) => item?.value !== authUser?.id
            ),
          visibleRightIcon: true,
          disabled: !countryId,
        },
      ],
      financial: [
        {
          name: 'form_group_2',
          formGroup: {
            title: t('financial_metrics'),
            formSection: true,
            className: '!mt-4 !mb-0',
            color: countryId ? 'primaryText' : 'colorIconForth2',
          },
          wrapStyle: classes.formItemWrapStyle,
          cp: () => null,
        },
        {
          name: 'expectedTaxTerm',
          label: t('tax_term'),
          isFirstHalfWidth: true,
          forceVisibleError: true,
          cp: 'asyncAutoComplete',
          type: 'text',
          visibleRightIcon: true,
          rightIconProps: { name: 'search' },
          url: Endpoints.App.Common.searchTaxterm,
          showDropDownWithoutEnteringAnything: true,
          params: {
            countryCode: countryId?.toLowerCase(),
          },
          normalizer: lookupNormalizer.searchTaxTerms,
          disabled: !countryId,
        },
        {
          name: 'expectedMarkup',
          label: t('mark_up_percent'),
          isSecondHalfWidth: true,
          type: 'number',
          forceVisibleError: true,
          disabled: !countryId,
          cp: 'input',
        },
      ],
      clearance: [
        {
          formGroup: {
            title: t('clearance'),
            formSection: true,
            className: '!mt-4',
            color: countryId ? 'primaryText' : 'colorIconForth2',
          },
          name: 'criminalRecord',
          cp: 'asyncAutoComplete',
          // disabledReadOnly: isLoboxUser,
          wrapStyle: classes.formItemWrapStyle,
          label: t('clearance_type'),
          url: lookupEndpoints.getCriminalRecords,
          normalizer: lookupNormalizer.searchCriminalRecords,
          params: {
            countryCode: countryId?.toLowerCase() || '',
          },
          rightIconProps: { name: 'search' },
          onlyChooseFromOptions: true,
          disabled: !countryId,
        },
      ],
      documents: [
        {
          formGroup: {
            title: t('document'),
            formSection: true,
            className: '!mt-4',
            color: countryId ? 'primaryText' : 'colorIconForth2',
          },
          wrapStyle: classes.formItemWrapStyle,
          name: 'fileIds',
          cp: 'attachmentPicker',
          uploadUrl: storageEndPoints.uploadPrivateFile,
          // removeApi: schedulesApi.deleteFile,
          getApi: storageApis.getFile,
          visibleOptionalLabel: false,
          forceVisibleError: true,
          uploadApi: fileApi.uploadFile,
          classNames: {
            uploadFile: '!hidden !hide',
          },
          onChange: (newFiles: AttachmentPickerProps['value']) => {
            const { fileIds = [] } = values ?? {};
            const newFileIds = newFiles.map(({ id }) => id);
            setTimeout(() => {
              setFieldValue('fileIds', [...newFileIds, ...fileIds]);
            }, 1000);
          },
          labels: {
            fileTypes: translateReplacer(t('acc_file_types_x_max_x'), [
              `${pickTypes.attachment}, max 100MB`,
            ]),
          },
        },
      ],
    };
  }, [t, authUser, values, isLoboxUser, isMoreThanTablet]);

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <Flex>
        <DynamicFormBuilder
          className={classes.formBuilder}
          groups={formGroups.country}
        />
        <Typography size={13} height={15} color="border" className="ml-16 mt-4">
          {t('residency_location_help_text')}
        </Typography>
      </Flex>
      <Info
        className="!p-12"
        text={t('residency_location_required_alert_message')}
      />
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={formGroups.ssn}
      />
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={formGroups.visa}
      />
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={formGroups.financial}
      />
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={formGroups.clearance}
      />
      <DynamicFormBuilder
        className={classes.formBuilder}
        groups={formGroups.documents}
      />
      <PrivateAttachmentsList ids={values.attachments ?? []} />
    </ModalTransitionWrapper>
  );
};

export default CandidateLegalBody;

import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useGetAllProviders } from '@shared/components/molecules/EventsIntegration/utils/useGetAllProviders';
import { ProviderType } from 'shared/components/molecules/EventsIntegration/utils/type';
import {
  MeetingChannel,
  type MeetingChannelOption,
} from 'shared/types/schedules/schedules';
import Button from 'shared/uikit/Button';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTheme from 'shared/uikit/utils/useTheme';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useSchedulesUrlState } from '../useSchedulesUrlState';
import { MeetingToolIcon, MeetingToolOption } from './MeetingToolOption';
import classes from './useMeetingChannelOptions.module.scss';
import type { OnRightIconClickProps } from 'shared/uikit/TextInput';

export default function useMeetingChannelOptions() {
  const { t } = useTranslation();
  // Note: Type is declared in /src/shared/uikit/RadioGroup/index.tsx
  const options = useMemo(
    () => [
      {
        label: t('lobox_account'),
        hint: {
          content: t('lobox_account_hint'),
          tooltipWrapperProps: { className: classes.tooltipWrapper },
        },
        value: MeetingChannel.LOBOX_ACCOUNT,
      },
      {
        label: t('personal_account'),
        hint: {
          content: t('personal_account_hint'),
          tooltipWrapperProps: { className: classes.tooltipWrapper },
        },
        value: MeetingChannel.PERSONAL_ACCOUNT,
        children: <PersonalAccountOptionChildren />,
      },
      {
        label: t('cr_meet_via_custom_link'),
        hint: {
          content: t('cr_meet_via_custom_link_hint'),
          tooltipWrapperProps: { className: classes.tooltipWrapper },
        },
        value: MeetingChannel.CUSTOM_LINK,
        children: <CustomLinkOptionChildren />,
      },
    ],
    [t]
  );

  return options;
}

const PersonalAccountOptionChildren = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const { values } = useFormikContext<any>();
  const {
    state: { scheduleEventsPanelData },
    setScheduleEventsPanelData,
  } = useSchedulesUrlState();

  const { data } = useGetAllProviders(ProviderType.Conference);
  const providerOptions = useMemo(
    () =>
      data &&
      Object.values(data).reduce((acc, providers) => {
        const options = providers.map((provider) => ({
          label: provider.externalUserName,
          value: provider,
          disabled: provider.connected === false,
        }));
        acc.push(...options);

        return acc;
      }, [] as MeetingChannelOption[]),
    [data]
  );

  const meetingTool = useMemo(
    () => ({
      name: 'externalConferenceProvider',
      cp: 'dropdownSelect',
      label: t('account'),
      required: true,
      options: providerOptions,
      optionKey: (item: any) => `ecp-${item.value.id}`,
      className: classes.marginTop,
      renderItem: ({ item, isSelected }: any) => (
        <MeetingToolOption
          providerType={item?.value.type}
          providerLabel={item?.label}
          isSelected={isSelected}
          disabled={item.disabled}
        />
      ),
      leftIcon: values?.externalConferenceProvider?.value && (
        <MeetingToolIcon
          providerType={values?.externalConferenceProvider?.value.type}
        />
      ),
    }),
    [providerOptions, t, values?.externalConferenceProvider?.value]
  );

  const group = useMemo(() => [meetingTool], [meetingTool]);

  const onAddAccountClick = () =>
    setScheduleEventsPanelData({
      ...scheduleEventsPanelData,
      isInIntegrations: true,
    });

  return (
    <>
      {providerOptions && providerOptions.length > 0 && (
        <DynamicFormBuilder groups={group} />
      )}
      <Button
        leftIcon="plus"
        label={t('add_account')}
        schema={isDark ? 'dark-gray' : 'semi-transparent'}
        className={classes.marginTop}
        onClick={onAddAccountClick}
      />
    </>
  );
};

const CustomLinkOptionChildren = () => {
  const { t } = useTranslation();
  const { setFieldValue, getFieldProps } = useFormikContext<any>();

  const onRightIconClick = async ({ inputRef }: OnRightIconClickProps) => {
    const text = await navigator?.clipboard?.readText();
    if (typeof text === 'string') {
      setFieldValue('customLink', text);
    }
    inputRef.current.focus();
  };
  const fieldProps = getFieldProps('customLink');
  const group = [
    {
      name: 'customLink',
      label: t('meeting_link'),
      cp: 'input',
      required: true,
      inputWrapClassName: classes.textInput,
      onRightIconClick,
      wrapStyle: 'mb-12',
      rightIcon: (
        <Button
          label={t('paste')}
          schema="transparent-brand"
          className={classes.cLButton}
          disabled={fieldProps?.value}
        />
      ),
    },
  ];

  return <DynamicFormBuilder groups={group} />;
};

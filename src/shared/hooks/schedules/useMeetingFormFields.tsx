import { useFormikContext } from 'formik';
import { useMemo, useCallback } from 'react';
import BaseButton from '@shared/uikit/Button/BaseButton';
import Typography from '@shared/uikit/Typography';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { SCHEDULE_EVENT_DESCRIPTION_MAX_LENGTH } from 'shared/constants/enums';
import storageApis from 'shared/utils/api/storage';
import { schedulesDb } from 'shared/utils/constants';
import { storageEndPoints } from 'shared/utils/constants/servicesEndpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useMeetingChannelOptions from './useMeetingChannelOptions';
import classes from './useMeetingChannelOptions.module.scss';
import { useScheduleFormFieldOptions } from './useScheduleFormFieldOptions';
import type { MeetingDetails } from '@shared/types/schedules/schedules';

export const useMeetingFormFields = (
  permissions?: MeetingDetails['permissions'],
  onChooseTemplate?: () => void
) => {
  const { t } = useTranslation();
  const { setFieldValue } = useFormikContext<any>();
  const { handleChangeParams, allParams } = useCustomParams();

  const meetingChannelOptions = useMeetingChannelOptions();
  const { attendeePermissionsOptions } = useScheduleFormFieldOptions();

  const openTemplateSelection = useCallback(() => {
    if (onChooseTemplate) {
      onChooseTemplate?.();
    } else {
      handleChangeParams({
        add: { chooseTemplate: 'true' },
      });
    }
  }, [handleChangeParams]);

  const meetingModel = useMemo(
    () => ({
      name: 'contactType',
      cp: 'dropdownSelect',
      label: t('meeting_model'),
      required: true,
      options: schedulesDb.contactType,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, permissions]
  );
  const meetingRemind = useMemo(
    () => ({
      name: 'remind',
      cp: 'dropdownSelect',
      label: t('meeting_reminder'),
      options: schedulesDb.meetingReminder,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [permissions, t]
  );

  const meetingChannel = useMemo(
    () => ({
      formGroup: {
        color: 'smoke_coal',
        title: t('meeting_channel'),
        className: classes.formGroupHeader,
      },
      classNames: {
        itemWrapper: classes.checkboxContainer,
      },
      wrapStyle: 'mt-20',
      className: classes.checkboxGroup,
      cp: 'radioGroup',
      name: 'meetingChannel',
      options: meetingChannelOptions,
      label: t('meeting_channel'),
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [meetingChannelOptions, permissions, t]
  );

  const attachments = useMemo(
    () => ({
      name: 'attachmentFiles',
      cp: 'attachmentPicker',
      uploadUrl: storageEndPoints.uploadPrivateFile,
      // removeApi: schedulesApi.deleteFile,
      getApi: storageApis.getFile,
      label: t('attachment'),
      visibleOptionalLabel: false,
      onChange: (attachment: any) => {
        setFieldValue('attachmentFiles', attachment);
      },
      disabledReadOnly: permissions && !permissions.MODIFY_MEETING,
    }),
    [permissions, setFieldValue, t]
  );

  const attendeePermissions = useMemo(
    () => ({
      name: 'attendeePermissions',
      formGroup: {
        color: 'smoke_coal',
        title: t('attendee_permissions'),
        className: classes.formGroupHeader,
      },
      cp: 'checkBoxGroup',
      classNames: {
        container: classes.checkboxContainer,
      },
      wrapStyle: 'mt-5',
      className: classes.checkboxGroup,
      options: attendeePermissionsOptions,
      label: t('attendee_permissions'),
      disabledReadOnly: permissions && !permissions?.IS_CREATOR,
      onChange(newValue: typeof attendeePermissionsOptions, { meta }: any) {
        const seeGuestsListOption = attendeePermissionsOptions[2];
        // const inviteOthersOption = attendeePermissionsOptions[1];
        // const modifyEventOption = attendeePermissionsOptions[0];
        const hasGuestListOption = newValue.find(
          (item) => item.value === seeGuestsListOption.value
        );
        // const hasInviteOthersOption = newValue.find(
        //   (item) => item.value === seeGuestsListOption.value
        // );

        if (newValue.length > 0 && !hasGuestListOption) {
          if (meta.value?.includes(seeGuestsListOption)) {
            newValue.length = 0;
          } else {
            newValue.push(seeGuestsListOption);
          }
        }
        // if (newValue.length > 1 && !hasInviteOthersOption) {
        //   if (meta.value?.includes(inviteOthersOption)) {
        //     const modifyEventIndex = newValue.findIndex(
        //       (item) => item.value === modifyEventOption.value
        //     );
        //     newValue.splice(modifyEventIndex, 1);
        //   } else {
        //     newValue.push(inviteOthersOption);
        //   }
        // }
      },
    }),
    [t, attendeePermissionsOptions, permissions]
  );

  const room = useMemo(
    () => ({
      label: t('address_details'),
      name: 'locationDetails',
      cp: 'input',
      helperText: t('example_address'),
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, permissions]
  );

  const description = useMemo(
    () => ({
      name: 'description',
      cp: 'richtext',
      label: t('description'),
      maxLength: SCHEDULE_EVENT_DESCRIPTION_MAX_LENGTH,
      visibleCharCounter: true,
      showEmoji: false,
      magicUrl: true,
      disabledReadOnly: permissions && !permissions.MODIFY_MEETING,
      additionalComponent: (
        <BaseButton
          onClick={(e) => {
            e.stopPropagation();
            openTemplateSelection();
          }}
        >
          <Typography className="!text-brand text-sm font-bold">
            {t('choose_template')}
          </Typography>
        </BaseButton>
      ),
      emojiClassName: '!absolute !right-16 !top-16 !z-[999999] !m-0',
    }),
    [t, permissions, openTemplateSelection]
  );

  return {
    meetingModel,
    meetingRemind,
    meetingChannel,
    attachments,
    attendeePermissions,
    room,
    description,
  };
};

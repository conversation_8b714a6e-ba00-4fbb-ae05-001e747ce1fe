import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import IconButton from '@shared/uikit/Button/IconButton';
import Divider from '@shared/uikit/Divider';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import Switch from '@shared/uikit/Switch';
import Tooltip from '@shared/uikit/Tooltip';
import {
  ChangePipelineApplicantTrack,
  ChangePipelineColor,
  deletePipeline,
} from '@shared/utils/api/jobs';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { usePipelinePageCtx } from '../../../PipelinePageProvider';
import DeleteStageModal from './StageMoreOptions/DeleteStageModal';
import PipelineActivitiesModal from './StageMoreOptions/PipelineActivitiesModal';
import StageChangeColorOption from './StageMoreOptions/StageChangeColorOption';
import StageSortOption from './StageMoreOptions/StageSortOption';
import type {
  PipelineStageSortBy,
  PipelineStageFilter,
} from '@shared/types/jobsProps';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import type { colorsKeys } from 'shared/uikit/helpers/theme';

interface StageMoreOptionsProps {
  stage: PipelineInfo;
  onBulk: (id?: string) => void;
  isBulked?: boolean;
  hasActiveCandidate?: boolean;
  filter: PipelineStageFilter;
  setFilter: (filter: PipelineStageFilter) => void;
}

const StageMoreOptions: React.FC<StageMoreOptionsProps> = ({
  stage,
  onBulk,
  isBulked,
  hasActiveCandidate,
  filter,
  setFilter,
}) => {
  const { t } = useTranslation();
  const { refetch } = usePipelinePageCtx();
  const [openDeleteConfirm, setOpenConfirmModal] = useState<number>();
  const [openActivitiesModal, setOpenActivitiesModal] = useState(false);
  const canDelete = stage.type === 'CUSTOMIZE' || stage.type === 'INTERVIEW';
  const { mutate: onDeletePipeline, isLoading: isDeleteing } = useMutation({
    mutationFn: deletePipeline,
    onSuccess: () => refetch(),
    onSettled: () => setOpenConfirmModal(undefined),
  });

  const handleOpenAutomationDrawer = () => {
    openMultiStepForm({
      formName: 'automation',
      data: stage,
      type: 'main',
    });
  };

  const {
    mutate: mutateApplicantTrack,
    isLoading: isChangeApplicantTrackLoading,
  } = useMutation({
    mutationFn: ChangePipelineApplicantTrack,
    onSuccess: () => refetch(),
    onSettled: () => setOpenConfirmModal(undefined),
  });
  const { mutate: mutateColor, isLoading: isChangeColorLoading } = useMutation({
    mutationFn: ChangePipelineColor,
    onSuccess: () => refetch(),
    onSettled: () => setOpenConfirmModal(undefined),
  });
  const onChangeApplicantTrack = (
    e?: React.MouseEvent<HTMLInputElement, MouseEvent>
  ) => {
    mutateApplicantTrack({
      id: stage.id ?? '',
      applicantTrack: !stage.applicantTrack,
    });
  };
  const onChangeSort = (sort: PipelineStageSortBy) => {
    setFilter({ sortBy: sort });
  };
  const onChangeColor = (color: colorsKeys) => {
    mutateColor({
      id: stage.id ?? '',
      color,
    });
  };
  const onToggleBulk = () => {
    onBulk(isBulked ? undefined : stage.id);
  };

  return (
    <>
      <PopperMenu
        placement="bottom-end"
        closeOnScroll
        buttonComponent={
          <IconButton
            type="fas"
            name="ellipsis-h"
            size="md"
            className="ml-auto"
            disabled={isChangeApplicantTrackLoading || isChangeColorLoading}
          />
        }
      >
        <PopperItem
          onClick={onChangeApplicantTrack}
          iconName="user-applicants"
          iconType="far"
          label={t('applicant_track')}
          iconSize={20}
          action={
            <Switch
              className="!p-0 flex !flex-grow-[unset] ml-auto"
              value={stage.applicantTrack}
            />
          }
          className="min-w-[280px]"
        />
        <PopperItem
          onClick={onToggleBulk}
          iconName="bulk"
          iconType="far"
          label={t('bulk_action')}
          iconSize={20}
          action={
            <Switch
              className="!p-0 flex !flex-grow-[unset] ml-auto"
              value={isBulked}
              onClick={onToggleBulk}
            />
          }
          className="min-w-[280px]"
        />
        <Divider />
        <PopperItem
          onClick={handleOpenAutomationDrawer}
          iconName="automate"
          iconType="far"
          label={t('automate')}
          iconSize={20}
          className="w-[280px]"
        />
        <PopperItem
          onClick={() => setOpenActivitiesModal(true)}
          iconName="trend"
          iconType="far"
          label={t('activities')}
          iconSize={20}
          className="w-[280px]"
        />
        <Divider />
        <StageSortOption onSort={onChangeSort} sort={filter.sortBy} />
        <StageChangeColorOption
          onChangeColor={onChangeColor}
          color={stage.color ?? 'brand'}
        />
        <Divider />
        <Tooltip
          disabled={canDelete}
          trigger={
            <PopperItem
              onClick={() => {
                const div = document.querySelector(
                  `[data-rfd-droppable-id="${stage.id}"]`
                );
                if (!div) return;
                setOpenConfirmModal(div.getBoundingClientRect().left);
              }}
              iconName="trash"
              iconType="far"
              label={t('delete')}
              iconSize={20}
              className="w-[280px] !items-start"
              secondaryLabel={t('delete_stage_desc2')}
              secondLabelTruncated
              labelsContainerClassName="flex-1 overflow-hidden"
              secondaryLabelColor="secondaryDisabledText"
              secondaryLabelSize={12}
              secondaryLabelProps={{ height: 18, showTooltip: false }}
              disabled={!canDelete}
            />
          }
        >
          {t('delete_default_stage_alert')}
        </Tooltip>
      </PopperMenu>
      {!!openDeleteConfirm && (
        <DeleteStageModal
          onClose={() => setOpenConfirmModal(undefined)}
          onDelete={() => onDeletePipeline(stage.id ?? '')}
          style={{ left: `${openDeleteConfirm}px` }}
          isDisabled={hasActiveCandidate}
          isDeleteing={isDeleteing}
        />
      )}
      {openActivitiesModal && (
        <PipelineActivitiesModal
          onClose={() => setOpenActivitiesModal(false)}
          stage={stage}
        />
      )}
    </>
  );
};

export default StageMoreOptions;
